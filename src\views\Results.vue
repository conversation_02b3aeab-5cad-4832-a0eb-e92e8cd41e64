<template>
    <div class="bg">
        <el-image fit="cover" :src="results" style="width: 100%;height: 100%;"></el-image>
        <PartnerSchool />
        <OnlineClass />
        <Resource />

    </div>

</template>

<script setup>
import results from '@/assets/results.png'
import PartnerSchool from '@/components/Results/PartnerSchool.vue'
import OnlineClass from '@/components/Results/OnlineClass.vue'
import Resource from '@/components/Results/Resource.vue'

</script>

<style lang="scss" scoped>


</style>