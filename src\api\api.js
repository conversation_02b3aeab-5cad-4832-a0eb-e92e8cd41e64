import request from '@/utils/request.js'
import axios from 'axios'

// 登录接口
export function login(data) {
	return request({
		method: 'post',
		url: '/login',
		data,
		type: 2
	});
}

// 退出登录接口
export function logout() {
	return request({
		method: 'get',
		url: '/logout'
	});
}
export function bannerList() {
	return request({
		method: 'get',
		url: `/ear/portal/advertisingspace/list`,
	});
}
export function courseList(params) {
	return request({
		method: 'get',
		url: '/aigc/business/courseinfo/listCourse',
		params
	});
}
// 新闻列表
export function newsList(params) {
	return request({
		method: 'get',
		url: `/website/bussiness/afficheinfo/list`,
		params
	});
}
// 新闻详情
export function newsDetail(id) {
	return request({
		method: 'get',
		url: `/website/bussiness/afficheinfo/info/${id}`,
	});
}
// 资源库案例列表
export function resourceList(params) {
	return request({
		method: 'get',
		url: `/website/bussiness/resourcefile/list`,
		params
	});
}
// 获取教材列表
export function textbookList(params) {
	return request({
		method: 'get',
		url: '/cloud/business/materialinfo/list',
		params
	});
}
// 专业资源列表
export function majorResourceList(params) {
	return request({
		method: 'get',
		url: `/cloud/business/majorresource/list`,
		params
	});
}
//专业资源数量
export function materialInfoCount(params) {
	return request({
		method: 'get',
		url: `/cloud/business/majorresource/queryCount`,
		params
	});
}
// 微专业列表
export function micromajorList(params) {
	return request({
		method: 'get',
		url: `/cloud/business/pchome/list`,
		params
	});
}
// 微专业详情
export function micromajorDetail(id) {
	return request({
		method: 'get',
		url: `/cloud/business/pchome/info/${id}`,
	});
}

// 教学团队列表
export function teachteamList(params) {
	return request({
		method: 'get',
		url: `/cloud/business/teachingteam/list`,
		params
	});
}
// 课程视频章节列表
export function courseVideoList(params) {
	return request({
		method: 'get',
		url: `/cloud/business/coursevideo/list`,
		params
	});
}
// 课程视频小节和视频列表
export function courseVideoQueryAll(params) {
	return request({
		method: 'get',
		url: `/cloud/business/coursevideo/queryListAll`,
		params
	});
}

//专业评价列表
export function evaluationList(params) {
	return request({
		method: 'get',
		url: '/cloud/business/majorevaluate/list',
		params
	});
}

//保存专业评价
export function saveEvaluation(data) {
	return request({
		method: 'post',
		url: '/cloud/business/majorevaluate/save',
		data
	});
}

//专业评价统计
export function evaluationStatistics(params) {
	return request({
		method: 'get',
		url: '/cloud/business/majorevaluate/majorStatistics',
		params
	});
}

// 获取字典数据列表
export function queryDictItemList() {
	return request({
		method: 'get',
		url: '/cloud/business/pchome/queryDictItemList',
	});
}

// 保存用户报名信息
export function saveUserSign(data) {
	return request({
		method: 'post',
		url: '/digital/controller/usersign/save',
		data
	});
}

// DeepSeek AI 聊天接口
export function deepseekChat(messages, useReasoner = false) {
	const apiKey = import.meta.env.VITE_DEEPSEEK_API_KEY;

	if (!apiKey || apiKey === 'YOUR_DEEPSEEK_API_KEY') {
		return Promise.reject(new Error('请在环境变量中配置有效的 DeepSeek API Key'));
	}

	// 创建独立的axios实例用于DeepSeek API
	const deepseekRequest = axios.create({
		baseURL: 'https://api.deepseek.com',
		timeout: 600000,
		headers: {
			'Content-Type': 'application/json',
			'Authorization': `Bearer ${apiKey}`
		}
	});

	// 根据参数选择模型
	const model = useReasoner ? 'deepseek-reasoner' : 'deepseek-chat';

	return deepseekRequest({
		method: 'post',
		url: '/chat/completions',
		data: {
			model: model,
			messages: messages,
			stream: false,
			temperature: 0.7,
			max_tokens: 2000
		}
	});
}



export function listAll(params) {
	return request({
		method: 'get',
		url: `/aigc/business/tagitem/listAll`,
		params
	});
}
export function jiaoyanlist(params) {
	return request({
	  method: 'get',
	  url: '/aigc/business/teachingresearchoffice/list',
	  params
	});
  }
  export function offLectureInfo(data) {
	return request({
	  method: 'get',
	  url: `/ear/portal/expertchairinfo/info?id=${data.id}&userId=${data.userId}`,
	});
  }
  export function xianxiaInfo(data) {
	return request({
	  method: 'get',
	  url: `/ear/portal/expertchairinfo/info?id=${data.id}`,
	});
  }
  export function jiaoyanInfo(data) {
	return request({
	  method: 'get',
	  url: `/aigc/business/activity/info?id=${data.id}`,
	});
  }
  export function jiaoyanDetail(data) {
	return request({
	  method: 'get',
	  url: `/aigc/business/teachingresearchoffice/info/${data.id}`,
	});
  }
  
  
  export function offLectureSave(data) {
	return request({
	  method: 'post',
	  url: '/ear/portal/expertchairuserrelation/save',
	  data
	});
  }
  //新增教研室
  export function saveTeachingOffice(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/teachingresearchoffice/saveTeachingUser',
	  data,
		  headers: {
			  'Content-Type': 'application/json;charset=UTF-8'
		  },
	});
  }
  
  //标签类型管理
  export function labelList(params) {
	return request({
	  method: 'get',
	  url: '/aigc/business/tagitem/listAll',
	  params
	})
  }
  
  // 更新教研室
  export function updateTeachingOffice(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/teachingresearchoffice/update',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  
  // 删除教研室
  export function deleteTeachingOffice(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/teachingresearchoffice/delete',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  
  // 获取教研室主理人列表
  export function getResearchOfficeUsers(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/teachingresearchoffice/getAllResarchOfficeUser',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  // 获取教研室资源列表
  export function getResourceList(params) {
	return request({
	  method: 'get',
	  url: '/aigc/business/resourcedetail/list',
	  params,
	});
  }
  // 保存资源
  export function saveResource(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/resourcedetail/save',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  // 申请加入教研室
  export function applyJoinResearchOffice(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/teachingresearchoffice/applyResarchOfficeUser',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  
  
  // 更新教研室用户状态
  export function updateOfficeUser(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/teachingresearchoffice/updateOfficeUser',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  // 主理人详情
  export function getUserInfo(data) {
	return request({
	  method: 'post',
	  url: `/aigc/business/userlogin/info`,
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  export function getPersonInf(data) {
	return request({
	  method: 'post',
	  url: `/aigc/business/userlogin/info`,
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  
  // 关注用户
  export function followUser(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/fans/save',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  // 取消关注用户
  export function unfollowUser(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/fans/delete',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  // 获取关注和粉丝数量
  export function getFansCount(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/fans/getCount',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  
  // 获取资源详情
  export function getResourceDetail(id) {
	return request({
	  method: 'get',
	  url: `/aigc/business/resourcedetail/info/${id}`,
	});
  }
  
  // 更新资源
  export function updateResource(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/resourcedetail/update',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  
  // 删除资源
  export function deleteResource(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/resourcedetail/delete',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  
  // 获取我的教材列表
  export function getMyBookList(params) {
	return request({
	  method: 'get',
	  url: '/aigc/business/materialinfo/list',
	  params,
	});
  }
  
  // 保存教材
  export function saveMaterial(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/materialinfo/save',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  
  // 获取教材详情
  export function getMaterialDetail(id) {
	return request({
	  method: 'get',
	  url: `/aigc/business/materialinfo/info/${id}`,
	});
  }
  
  // 更新教材
  export function updateMaterial(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/materialinfo/update',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  
  // 删除教材
  export function deleteMaterial(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/materialinfo/delete',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  
  // 获取活动列表
  export function getActivityList(params) {
	return request({
	  method: 'get',
	  url: '/aigc/business/activity/list',
	  params
	});
  }
  
  // 保存活动
  export function saveActivity(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/activity/save',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  
  // 更新活动
  export function updateActivity(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/activity/update',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  
  // 删除活动
  export function deleteActivity(data) {
	return request({
	  method: 'post',
	  url: '/aigc/business/activity/delete',
	  data,
	  headers: {
		'Content-Type': 'application/json;charset=UTF-8'
	  },
	});
  }
  
  // 获取活动详情
  export function getActivityInfo(id) {
	return request({
	  method: 'get',
	  url: `/aigc/business/activity/info?id=${id}`,
	});
  }