<template>
    <div class="bg">
        <div class="w1280">
            <back>返回</back>
            <div class="bookList">
                <div class="bookItem" v-for="(item,index) in bookList" :key="index">
                    <img src="@/assets/1.png" alt="" class="bookImg">
                    <div class="name text-ellipsis-1">{{ item.name }}</div>
                    <div class="time">
                        <img src='@/assets/time.png' alt="" class="userImg">
                        <span class="text">{{ item.time }}</span>
                    </div>
                </div>
            </div>
            <el-pagination
                v-model:current-page="pageParams.pageNum"
                v-model:page-size="pageParams.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :background="true"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                style="margin-top: 20px; justify-content: center;"
            />
        </div>
    </div>
</template>
    
<script setup>
const pageParams = ref({
    pageNum: 1,
    pageSize: 10
})
const total = ref(0)

const bookList =ref([
    {
        name:'幼儿园教育活动设计与施计与施计与施',
        time:'2025-07-26 10:31:22'
    },
    {
        name:'幼儿园教育活动设计与施计与施计与施',
        time:'2025-07-26 10:31:22'
    },
    {
        name:'幼儿园教育活动设计与施计与施计与施',
        time:'2025-07-26 10:31:22'
    },
    {
        name:'幼儿园教育活动设计与施计与施计与施',
        time:'2025-07-26 10:31:22'
    },
    {
        name:'幼儿园教育活动设计与施计与施计与施',
        time:'2025-07-26 10:31:22'
    },
    {
        name:'幼儿园教育活动设计与施计与施计与施',
        time:'2025-07-26 10:31:22'
    },
    {
        name:'幼儿园教育活动设计与施计与施计与施',
        time:'2025-07-26 10:31:22'
    },
])

const handleSizeChange = (val) => {
    pageParams.value.pageSize = val
    pageParams.value.pageNum = 1
    // fetchBookList()
}

const handleCurrentChange = (val) => {
    pageParams.value.pageNum = val
    // fetchBookList()
}

// 接口调用示例
const fetchBookList = () => {
    console.log('调用接口参数:', pageParams.value)
    // api.getBookList(pageParams.value).then(res => {
    //     bookList.value = res.data.list
    //     total.value = res.data.total
    // })
}
</script>
      
<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
  height: 100vh;
  padding: 31px;
}
.bookList{
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(5, 1fr);
    margin-top: 20px;
}
.bookItem{
    width: 240px;
    height: 358px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    padding: 24px;
    padding-bottom: 16px;
}
.bookImg{
    width: 192px;
    height: 254px;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #E1E4EB;
}
.time{
    display: flex;
    align-items: center;
}
.name{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #2D2F33;
    margin-top: 12px;
    margin-bottom: 8px;
}
.userImg{
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.text{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
}
</style>
  