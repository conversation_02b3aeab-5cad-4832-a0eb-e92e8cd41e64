import axios from "axios";
import router from "@/router";
import { ElMessage } from 'element-plus';

const { VITE_GLOB_BASE_URL, VITE_GLOB_API_URL, DEV, VITE_USE_MOCK, VITE_PORT, VITE_GLOB_FILE_URL } =
  import.meta.env;
const baseURL = VITE_GLOB_BASE_URL;

// 创建axios实例
const service = axios.create({
  baseURL, // URL地址
  timeout: 120 * 1000, // 超时时间
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    config.headers = {
      nonce: Date.parse(new Date()),
      clientType: "H5",
      clientVersion: "2.2.1",
      timestamp: Date.parse(new Date()),
    };

    // 添加token到请求头
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = ` ${token}`;
    }

    if (config.type == 2) {
      if (config.method === 'post') {
        config.headers['Content-Type'] = 'application/x-www-form-urlencoded';
      }
    } else if (config.type == 3 && config.method == 'post') {
      config.headers['Content-Type'] = 'multipart/form-data';
    } else {
      config.headers['Content-Type'] = 'application/json';
    }

    return Promise.resolve(config);
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data;

    if (res.status != 0) {
      if (res.status === 100403) {
        ElMessage({
          message: "登录状态过期，请重新登陆",
          type: 'warning',
          onClose: function () {
            router.replace('/login');
          }
        });
        return Promise.reject(new Error(res.message || 'Error'));
      }

      if (res.status === 502) {
        localStorage.setItem('backUrl', window.location.href);
        router.replace('/login');
        return Promise.reject(new Error(res.message || 'Error'));
      }

      ElMessage({ message: res.message || res.msg, type: 'error' });
      return Promise.reject(res.message || res.msg);
    } else {
      return Promise.resolve(res);
    }
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default service;