<template>
  <div class="mianbg">
    <div class="titlediv">
      <div class="title">我的教研室</div>
      <el-button class="defbtn" :icon="Plus" @click="showAdd">新增教研室</el-button>
    </div>
    <el-form class="searchdiv" inline>
      <el-form-item>
        <el-input 
        class="definput" 
        placeholder="请输入教研室名字"
        clearable
        v-model="pageBean.name"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input 
        class="definput" 
        placeholder="请输入主办单位"
        clearable
        v-model="pageBean.organizer"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button class="defbtn" :icon="Search" @click="searchAction">搜索</el-button>
      </el-form-item>
    </el-form> 
    <div class="listdiv">
      <Jysitem 
      v-for="item in 6" 
      :key="item"
      @reload="reloadAction"></Jysitem>
    </div>
    <el-pagination
      v-model:current-page="pageBean.pageNum"
      v-model:page-size="pageBean.pageSize"
      :background="true"
      layout="total, prev, pager, next, jumper"
      :total="total"
      @current-change="handleCurrentChange"
      style="margin-top: 20px; justify-content: center;"/>
      <addjys 
      ref="addjys"
      @reload="reloadAction"></addjys>
  </div>
</template>

<script setup>
import {
  Plus,
  Search
} from '@element-plus/icons-vue'
import { 
    jiaoyanlist, 
    labelList, 
    saveTeachingOffice, 
    jiaoyanDetail, 
    updateTeachingOffice, 
    deleteTeachingOffice 
} from '@/api/expert/index'
import { onMounted, reactive } from 'vue';
import Jysitem from './common/jysitem.vue';
import Addjys from './common/addjys.vue';
import { ElMessage } from 'element-plus';
const pageBean = reactive({
  pageNum:1,
  pageSize:6,
  name:'',
  organizer:'',
  isMyTeach:true,
  createBy:localStorage.getItem('id')
})
const total = ref(0)
const tableData = ref([])
const addjys = ref()

const loadData = () =>{
  jiaoyanlist(pageBean).then(res => {
      tableData.value = res.data
      total.value = res.page.total
  })
}
const searchAction = () =>{
  pageBean.pageNum = 1;
  loadData()
}
const handleCurrentChange = (page) =>{
  pageBean.pageNum = page;
  loadData()
}
const showAdd = () =>{
  addjys.value.show()
}
const reloadAction = ()=>{
  pageBean.pageNum = 1;
  loadData()
}
onMounted(()=>{
  loadData()
})
</script>

<style lang="scss" scoped>
.titlediv{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #2D2F33;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.searchdiv{
  margin-top: 20px;
  ::v-deep(.el-form-item){
    margin-right: 12px;
  }
}
.definput{
  width: 280px;
  height: 36px;
  border-radius: 4px;
  :v-deep(.el-input__inner){
    border: 1px solid #E1E4EB;
  }
}
.listdiv{
  display: flex;
  flex-wrap: wrap;
  .jiaoyanitem{
    width: calc(33.33% - 14px);
    margin-right: 20px;
  }
  .jiaoyanitem:nth-child(3n){
    margin-right: 0px;
  }
}

.topBtn{
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>