import service from '@/utils/request.js'
export function sendMessage(data) {
  return service.request({
    method: 'post',
    url: '/goldcourse/common/aliyun/sms/sendMessage',
    data
  });
}

export function queryAll(data) {
  return service.request({
    method: 'get',
    url: `/sf/business/tagitem/listAll`,
    params: data
  });
}


export function listAll(data) {
  return service.request({
    method: 'get',
    url: `/aigc/business/tagitem/listAll`,
    params: data
  });
}

