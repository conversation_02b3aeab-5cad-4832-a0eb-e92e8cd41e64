<template>
    <el-dialog 
    width="493" 
    :title="dialogTitle" 
    v-model="dialogVisible" 
    class="adddialog"
    @close="close" 
    :close-on-click-modal="false"
    :style="{
        '--el-dialog-padding-primary':'0px',
    }">
        <el-form 
        :rules="rules" 
        ref="addform" 
        :model="form" 
        label-position="top">
                <el-form-item label="分类名称" prop="name">
                    <el-input 
                        v-model="form.name" 
                        placeholder="请输入分类名称" 
                        show-word-limit 
                        maxlength="8" 
                        clearable>
                    </el-input>
                </el-form-item>
            </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button class="defbtn" @click="onSubmit">立即新增</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { onMounted, reactive } from 'vue';
const route = useRoute()
const getUrl = computed(()=>{
  return import.meta.env.VITE_GLOB_BASE_URL + '/aliyun/oss/uploadFiles'
})
const dialogVisible = ref(false)
const headerUrl = ref({
  Authorization: localStorage.getItem('token') 
})
var fileList = ref([])
const imgDetails = ref({ serviceName: 'web' })
const dialogTitle = ref('添加分类')
const form = reactive({
    id: '',
    name: '',
})
const rules = ref({
    name: [
        { required: true, message: '请输入教材名称', trigger: 'blur' }
    ]
})
const emits = defineEmits(['reload'])
const specialData = ref()
const addform = ref()
const show = (id) =>{
    dialogVisible.value = true;
    if (id) {
        form.id = id;
        dialogTitle.value = '编辑分类'
        loadInfo()
    }
}
function loadInfo(){

}
function resetForm() {
    fileList.value = [];
    for (let key in form) {
        form[key] = '';
    }
}
function close() {
    addform.value.resetFields();
    resetForm()
    dialogVisible.value = false
}
function onSubmit() {
    addform.value.validate((valid) => {
        if (valid) {
            const request = form.id ? 
                updateMaterial(form) : 
                saveMaterial(form);
            
            request.then(res => {
                if (res.status == 0) {
                    ElMessage.success(this.form.id ? '更新成功' : '保存成功');
                    dialogVisible.value = false;
                } else {
                    ElMessage.error(res.msg);
                }
            })
        } else {
            return false;
        }
    });
}
function handleRemove(file, fileList) {
    fileList.value = fileList;
    form.coverUrl = '';
    form.coverName = '';
    form.coverSize = '';
}
function handlePreview(file) {
    
}
function handleAvatarSuccess(res, file, fileList) {
    fileList.value = fileList;
    if (res.status == 0) {
        form.coverUrl = res.data.url;
        form.coverName = res.data.fileName;
        form.coverSize = res.data.size;
    } else {
        ElMessage.error('上传失败：' + res.msg);
    }
}

onMounted(()=>{

})
defineExpose({
    show
})
</script>

<style lang="scss" scoped>
.upload-demo {
  .el-upload {
    width: 100%;
  }
}

.dialog-footer {
    display: flex;
    justify-content: center;
}
</style>
<style lang="scss">
.adddialog{
    padding-bottom: 30px !important;
    .el-dialog__headerbtn{
        width: 24px !important;
        height: 24px !important;
        font-size: 20px !important;
        
        right: 30px;
        top: 16px;
        .el-icon{
            width: 24px;
            height: 24px;
            color: #2D2F33 !important;
        }
    }
    .el-dialog__body{
        padding:30px;
    }
    .el-dialog__header{
        border-bottom: 1px solid #E1E4EB ;
        padding: 16px 30px 12px;

    }
}
</style>