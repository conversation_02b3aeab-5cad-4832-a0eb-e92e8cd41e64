<template>
  <div class=" mdivcss">
    <div class="wid bgcss">
      <back>返回</back>
      <bread-crumb />
      <div class="topItem">
        <img :src="detailInfo.coverUrl" alt="" class="leftImg">
        <div class="jiaoDetail">
          <div class="title">{{ detailInfo.name }}</div>
          <div class="text">学分：2</div>
          <div class="text">学时：33</div>
          <div class="text">教师：{{ detailInfo.lecturerName }}</div>
          <div class="text">价格：123</div>
          <div class="joinBtn" @click="handleJoin">申请加入</div>
        </div>

      </div>
      <div class="middleContent">
        <div class="leftContent">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="课程目录" name="1">
              <directory :video-list="detailInfo.videoVoList"/>
            </el-tab-pane>
            <el-tab-pane label="课程公告" name="2">
              <div class="mainContent" v-html="detailInfo.content"></div>
            </el-tab-pane>
          </el-tabs>

        </div>
        <div class="rightContent">
          <div class="topTitle">
            <img src="@/assets/zhujiang.png" alt="">
            <div class="topText">主讲人简介</div>
          </div>
          <div class="introduction" v-html="detailInfo.lecturerIntro"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import back from '@/components/back.vue'
  import { resourceDetail } from '@/api/index.js'
  import directory from './directory.vue'
  import BreadCrumb from '@/components/common/Breadcrumb.vue'
  import { onMounted, ref } from 'vue'
  import { useRoute } from 'vue-router'
  const route = useRoute()
  const detailInfo = ref({})
  const activeName = ref('1')
  const getDetailInfo = () =>{
    const id = route.query.id
    if (!id) return
    resourceDetail(id)
      .then(res => {
        if (res.data) {
          detailInfo.value = res.data
        }
      })
  }
  getDetailInfo()
  const handleJoin = () =>{

  }
  const handleClick = (tab, event) =>{
    console.log(tab, event);
  }
</script>

<style lang="scss" scoped>
.mdivcss{
  padding: 20px 0px;
  background-color: #F5F7FA;
  min-height: calc(100vh - 93px);
}
.wid{
  width: 1280px;
}
.bgcss{
  margin: 0px auto;
  margin-bottom: 151px;
}
.topItem{
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  padding: 20px;
  display: flex;
}
.leftImg{
  width: 422px;
  height: 238px;
  margin-right: 30px;
}
.jiaoDetail{
  margin-right: 74px;
}
.title{
  height: 30px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #2D2F33;
  line-height: 30px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 16px;
}
.text {
  height: 24px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #4B4D4B;
  line-height: 24px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12px;
}
.text:last-child{
  margin-bottom: 0;
}
.joinBtn{
  display: flex;
  justify-content: center;
  align-items: center;
  width: 112px;
  height: 36px;
  background: #386CFC;
  border-radius: 99px 99px 99px 99px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-top: 24px;
  cursor: pointer;
}

.middleContent{
  display: flex;
  margin-top: 20px;
}
.leftContent{
  min-width: 743px;
  min-height: 400px ;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  margin-right: 20px;
  padding: 20px;
  box-sizing: border-box;
}
.rightContent{
  min-width: 557px;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  padding: 20px;
  box-sizing: border-box;
}
::v-deep(.el-tabs__item){
  height: 28px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #2E2F33;
  line-height: 28px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12px;
  &:hover{
    height: 28px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #386CFC;
    line-height: 28px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    }
    &.is-active{
      height: 28px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #386CFC;
      line-height: 28px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
}
::v-deep(.el-tabs__active-bar){
  width: 32px;
  height: 3px;
  background: #386CFC;
  border-radius: 20px 20px 20px 20px;
}
.topTitle{
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.topText{
  height: 24px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #2E2F33;
  line-height: 24px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-left: 8px;
}
.mainContent{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #4B4D4B;
  line-height: 28px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
