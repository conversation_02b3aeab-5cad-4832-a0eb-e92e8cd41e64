@font-face {
  font-family: "iconfont"; /* Project id 4948639 */
  src: url('iconfont.woff2?t=1749786923535') format('woff2'),
       url('iconfont.woff?t=1749786923535') format('woff'),
       url('iconfont.ttf?t=1749786923535') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-jilu:before {
  content: "\e624";
}

.icon-zichan:before {
  content: "\e625";
}

.icon-user:before {
  content: "\e621";
}

.icon-system:before {
  content: "\e620";
}

.icon-home:before {
  content: "\e622";
}

.icon-resource:before {
  content: "\e623";
}

