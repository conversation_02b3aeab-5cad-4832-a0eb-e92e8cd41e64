<template>
  <div class="headerBg">
    <div class="mainHeader">
      <div>
        <span @click="toHome">
          <img class="firstlogo" src="../assets/logo.png" />
        </span>
      </div>
      <div class="menudiv">
        <el-menu
          :default-active="activeIndex"
          :router="true"
          active-text-color="#386CFC"
          mode="horizontal"
          class="el-menu-title"
        >
          <el-menu-item index="/home">首页</el-menu-item>
          <el-menu-item index="/jiaoyanshi">教研室</el-menu-item>
          <el-menu-item index="/coursecenter">课程资源</el-menu-item>
          <el-menu-item index="/teachproject">培训项目</el-menu-item>
          <el-menu-item index="/teachtool">AI工具</el-menu-item>
          <el-menu-item index="/jxdandai">教师档案袋</el-menu-item>
        </el-menu>
      </div>
      <div class="user">
        <div class="loginRegister" v-show="loginBut">
          <div class="mt">
            <div class="logincss" @click="logindialogVisible = true">
              <span class="loginButton">登录</span>
            </div>

            <div class="registercss" @click="registerdialogVisible = true">
              <span class="registerButton"> 注册 </span>
            </div>
          </div>
        </div>

        <div class="touxiang" v-show="useUserStore.userType">
          <div class="touxiangPic">
            <img
              v-if="useUserStore.getPicUrl"
              :src="useUserStore.getPicUrl"
              alt
            />
            <img v-else src="../assets/touxiang1.jpg" alt="" />
          </div>
          <div class="loginName">
            <el-dropdown @command="handleCommand">
              <span class="el-dropdown-link" v-if="useUserStore.loginAccount">{{
                useUserStore.loginAccount
              }}</span>
              <span v-else-if="useUserStore.nickname">{{
                useUserStore.nickname
              }}</span>
              <span v-else class="el-dropdown-link">教师职称</span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="spacePer"
                    >个人中心</el-dropdown-item
                  >
                  <el-dropdown-item command="loginOut"
                    >退出登录</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      title
      v-model="logindialogVisible"
      width="440px"
      :before-close="handleClose"
      class="commonDialog loginDialog"
    >
      <div class="login">
        <div class="loginTit">
          <div></div>
          <div>账号密码登录</div>
          <div></div>
        </div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleFormRef"
          label-width="64px"
          class="demo-ruleForm loginForm"
          label-position="left"
        >
          <el-form-item label="用户名:" prop="username">
            <el-input
              v-model="ruleForm.username"
              placeholder="请输入用户名"
            ></el-input>
          </el-form-item>
          <el-form-item label="密   码:" prop="password">
            <el-input
              placeholder="请输入密码"
              v-model="ruleForm.password"
              type="password"
            ></el-input>
          </el-form-item>
        </el-form>
        <el-button
          class="saveButton"
          type="primary"
          @click="submitForm(ruleFormRef)"
          >立即登录</el-button
        >
        <div class="loginTip">
          <span>没有账号？</span>
          <span @click="register()">立即注册</span>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      title
      v-model="registerdialogVisible"
      width="440px"
      :before-close="handleCloseRegister"
      class="registerDialog loginDialog"
    >
      <div class="login register">
        <el-form
          :model="ruleFormRegister"
          :rules="rulesInfo"
          ref="ruleForm"
          label-width="64px"
          label-position="left"
        >
          <el-form-item label="手机号:" prop="mobilephone">
            <el-input
              v-model="ruleForm.mobilephone"
              placeholder="请输入手机号"
              class="mobilephone"
            ></el-input>
          </el-form-item>
          <el-form-item label="验证码:" prop="messageAuthCode">
            <el-input
              v-model="ruleForm.messageAuthCode"
              class="yanzhengma"
              placeholder="请输入验证码"
            ></el-input>
            <div class="phonesend" @click="getcode()" v-show="showcode">
              发送验证码
            </div>
            <div class="phonesend" v-show="!showcode">{{ count }}s</div>
          </el-form-item>
        </el-form>
        <el-button
          class="saveButton"
          type="primary"
          @click="phoneInfo('ruleForm')"
          >立即注册</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      v-model="infodialogVisible"
      width="493px"
      class="infoDialog"
      :show-close="false"
    >
      <template #header>
        <div class="my-header">
          <p class="dtitle">信息补充</p>
          <!-- <img class="close" src="../assets/close.png" alt="" @click="close" /> -->
        </div>
      </template>
      <div class="infobox">
        <el-form
          :model="ruleInfoForm"
          :rules="rulesRegister"
          ref="ruleForm"
          label-width="64px"
          label-position="right"
        >
          <el-form-item label="姓名:" prop="mobilephone">
            <el-input
              maxlength="4"
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的姓名"
              class="mobilephone"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="年龄:" prop="mobilephone">
            <el-input
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的年龄"
              class="mobilephone"
            ></el-input>
          </el-form-item>
          <el-form-item label="性别:">
            <el-select
              class="sexselect"
              v-model="ruleForm.region"
              placeholder="请选择您的性别"
            >
              <el-option label="Zone one" value="shanghai" />
              <el-option label="Zone two" value="beijing" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-radio-group v-model="ruleInfoForm.type">
              <el-radio value="1">高校</el-radio>
              <el-radio value="2">企业</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="单位:">
            <!-- <el-select
              class="sexselect"
              v-model="ruleForm.region"
              placeholder="请选择您的单位"
            >
              <el-option label="Zone one" value="shanghai" />
              <el-option label="Zone two" value="beijing" />
            </el-select> -->
            <el-input
              maxlength="20"
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的姓名"
              class="mobilephone"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="部门:" prop="mobilephone">
            <el-input
              maxlength="20"
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的部门"
              class="mobilephone"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="职称:" prop="mobilephone">
            <el-input
              maxlength="10"
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的职称"
              class="mobilephone"
              show-word-limit
            ></el-input>
          </el-form-item>
        </el-form>
        <el-button class="saveButton" type="primary">立即保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import { logout } from '@/api/api.js'
import CryptoJS from 'crypto-js'
import { userStore } from '@/stores/user.js'
import {
  login,
  verifyMobilePhone,
  userRegister,
  messageAuthCodeLogin,
  sendMessage,
  registerLogin,
} from '@/api/index.js'
import { reactive } from 'vue'
const route = useRoute()
const router = useRouter()
const useUserStore = userStore()
let menu = ref(null)
let top = ref()
let activePath = ref('')
let isTrue = ref(false)
let subMenuTitle = ref('企业大学')
let isScrolled = ref(false)
const count = ref(0)

let loginBut = ref(true)
let logindialogVisible = ref(false)
const activeIndex = ref(route.path)
const handleClose = () => {
  logindialogVisible.value = false
}
const handleCloseRegister = () => {
  registerdialogVisible.value = false
}

const showcode = ref(true)

const ruleFormRegister = reactive({
  mobilephone: '',
  messageAuthCode: '',
})

const registerdialogVisible = ref(false)

const ruleForm = reactive({
  username: '',
  password: '',
})

const rules = reactive({
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
})

const rulesRegister = {
  mobilephone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  messageAuthCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
  ],
}
const ruleFormRef = ref('')
const submitForm = (ruleFormRef) => {
  ruleFormRef.validate(async (valid, fields) => {
    if (valid) {
      const firstMd5 = CryptoJS.MD5(ruleForm.password).toString()
      const secondMd5 = CryptoJS.MD5(firstMd5).toString()
      let res = await login({
        username: ruleForm.username,
        password: secondMd5,
        captcha: 1,
        rememberMe: false,
      })
      if (res.status == 0) {
        useUserStore.changeLoginAccount(res.data.loginAccount)
        ElMessage.success('登录成功')
        userHandle(res)
        logindialogVisible.value = false
        loginBut.value = false
      } else {
        ElMessage.error(res.msg)
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

function getcode() {}

if (useUserStore.userType) {
  loginBut.value = false
} else {
  loginBut.value = true
}
const userHandle = (res) => {
  if (res.status == 0) {
    useUserStore.changePicUrl(res.data.logo)
    useUserStore.changeNickName(res.data.name)
    useUserStore.changeUserType(res.data.userType)
    useUserStore.changeSchoolId(res.data.schoolId)
    localStorage.setItem('id', res.data.id)
    localStorage.setItem('token', res.data.token)
  } else {
    ElMessage.error(res.msg)
  }
}

// 用户登录状态
const isLoggedIn = ref(false)
const userInfo = ref(null)

function updateSubMenuTitle(title) {
  subMenuTitle.value = title
}

function goToHome() {
  router.push('/site/home')
}

function openExternalLink(linkKey) {
  const url = externalLinks.value[linkKey]
  if (url) {
    window.open(url, '_blank')
  }
}

function goToLogin() {
  router.push('/login')
}

function checkLoginStatus() {
  const token = localStorage.getItem('token')
  const storedUserInfo = localStorage.getItem('userInfo')
  if (token && storedUserInfo) {
    try {
      isLoggedIn.value = true
      userInfo.value = JSON.parse(storedUserInfo)
    } catch (error) {
      console.error('解析用户信息失败:', error)
      handleLogout()
    }
  }
}

// 登出功能
function handleLogout() {
  logout()
    .then((res) => {
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      isLoggedIn.value = false
      userInfo.value = null
      ElMessage.success('已退出登录')
      useUserStore.clearState()
      router.push('/home')
    })
    .catch((error) => {
      console.error('退出登录失败:', error)
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      isLoggedIn.value = false
      userInfo.value = null
      ElMessage.success('已退出登录')
      router.push('/home')
    })
}

function handleCommand(command) {
  if (command == 'spacePer') {
    router.push({ path: '/center' })
  } else if (command == 'loginOut') {
    handleLogout()
  }
}

onMounted(() => {
  activePath.value = route.path
  checkLoginStatus()
})

onUnmounted(() => {})

function register() {
  registerdialogVisible.value = true
}

const infodialogVisible = ref(false)
const ruleInfoForm = ref({
  type: '1',
})
const rulesInfo = {
  mobilephone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  messageAuthCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
  ],
}
</script>

<style>
.commonDialog .el-dialog__body {
  padding: 0 50px !important;
}
.commonDialog {
  padding: 0 !important;
  height: 400px;
}
.commonDialog .el-dialog {
  width: 440px !important;
}
.commonDialogzhu .el-dialog {
  height: 500px;
}
.registerDialog .el-dialog__header {
  height: 48px;
}
.registerDialog .el-dialog__body {
  padding: 0 50px !important;
  padding-top: 40px !important;
}
.registerDialog {
  padding: 0 !important;
  height: 340px;
}
.registerDialog .el-dialog {
  width: 440px !important;
}

.loginDialog .el-dialog {
  height: 480px;
}
.wxDialog .el-dialog {
  height: 400px;
}

.registeForm .el-form-item__label {
  text-align: left;
}
.el-step__title.is-success {
  color: #ed7227 !important;
  font-size: 12px;
  font-family: Source Han Sans CN;
  font-weight: 400;
}
.infoDialog {
  padding: 0;
  width: 493px;
  background: #ffffff;
  box-shadow: 0px 2px 20px 0px rgba(33, 58, 128, 0.16);
  border-radius: 8px 8px 8px 8px;
  overflow: hidden;
}
.infoDialog .el-dialog__body {
  padding: 30px;
  padding-top: 10px;
}
.infoDialog .el-dialog__header {
  padding-bottom: 0px;
}
.infoDialog label {
  height: 40px;
  line-height: 40px;
}
</style>
<style lang="scss" scoped>
.saveButton {
  width: 100%;
  height: 42px;
  background: #386cfc;
  border-radius: 4px 4px 4px 4px;
  line-height: 42px;
  margin-top: 22px;
}
.sexselect {
  height: 40px;
  :deep(.el-select__wrapper) {
    height: 40px;
  }
}
.close {
  width: 24px;
  height: 24px;
  cursor: pointer;
  position: absolute;
  right: 24px;
}
.dtitle {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #2d2f33;
  padding-left: 30px;
}
.my-header {
  position: relative;
  padding-top: 20px;
  display: flex;
  width: 493px;
  height: 71px;
  background: linear-gradient(180deg, #e7edfe 0%, rgba(231, 237, 254, 0) 100%);
  justify-content: space-between;
}
:deep(.el-input__wrapper) {
  padding: 0 15px !important;
}
.mobilephone {
  height: 40px;
}
.flex {
  display: flex;
}
.wxtext {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 40px;
}
.a-text {
  color: #ed7227;
  cursor: pointer;
  text-align: center;
  font-size: 16px;
  margin-top: 20px;
}
.my-button {
  border: none;
  font-size: 16px;
  background: #0ecf69;
  height: 54px;
  width: 100%;
  transition: all 0.3s ease;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
}
.wechat-text {
  display: flex;
  align-items: center;
  justify-content: center;
}
.wechat-text span {
  margin-left: 10px;
}
.firstlogo {
  cursor: pointer;
  display: inline-block;
  vertical-align: top;
}
.el-dropdown {
  display: inline-block;
  position: relative;
  color: #606266;
  font-size: 14px;
  height: 42px;
  line-height: 42px;
  outline: none;
  span {
    outline: none;
  }
}

.logotext {
  display: inline-block;
  vertical-align: top;
  font-size: 20px;
  font-family: 'Source Han Sans CN';
  font-weight: 400;
  color: #333333;
  line-height: 72px;
}
.linelogo {
  display: inline-block;
  width: 1px;
  height: 30px;
  background: #dadada;
  margin: 21px 10px;
}
.mt {
  margin-top: 5px;
  display: flex;
}
.loginRegister {
  text-align: center;
}
.buttonFen {
  margin: 0 auto;
  margin-top: 40px;
  width: 96px;
  height: 32px;
  background: #3699ff;
  border-radius: 4px;
  text-align: center;
  color: #fff;
  line-height: 32px;
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}
.fenimg {
  margin: 0 auto;
  width: 48px;
  height: 48px;
  display: block;
  margin-top: 48px;
  margin-bottom: 20px;
}
.feneText {
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #333333;
  text-align: center;
}
.detail-news-title {
  font-size: 16px;
  font-weight: normal;
  text-align: center;
  padding-top: 10px;
  position: relative;
}
.detail-news-title::before {
  content: '';
  display: block;
  width: 40px;
  height: 1px;
  background: #ccc;
  position: absolute;
  left: 200px;
  margin-left: -80px;
  top: 18px;
}
.detail-news-title::after {
  content: '';
  display: block;
  width: 40px;
  height: 1px;
  background: #ccc;
  position: absolute;
  margin-left: 80px;
  right: 118px;
  top: 18px;
}
.detail-news-title span {
  padding: 0 10px;
  background: #fff;
  position: relative;
  z-index: 2;
  font-size: 18px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #333333;
}
.verification {
  font-size: 12px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #666666;
  margin-right: 40px;
  cursor: pointer;
}
.login .yanzhengma {
  width: 182px !important;
  height: 40px;
}
.logincss {
  width: 60px;
  height: 33px;
  line-height: 33px;
  text-align: center;
  background: #386cfc;
  border-radius: 4px 4px 4px 4px;
  margin-right: 16px;
  cursor: pointer;
}
.registercss {
  width: 60px;
  height: 33px;
  line-height: 33px;
  text-align: center;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #386cfc;
  cursor: pointer;
}
.loginButton {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
}
.textColor {
  color: #666666;
  display: block;
  height: 12px;
  font-size: 12px;
  margin-top: 5px;
}
.buttonCommon {
  font-size: 12px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  text-align: center;
}
.registerButton {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #386cfc;
}
.headerBg .mainHeader .user {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 13px;
  float: right;
  color: #333;
}
.phone {
  width: 10px;
  height: 17px;
  vertical-align: middle;
  display: inline-block;
  margin-right: 5px;
}
.imgtext,
.joinadmin {
  display: inline-block;
  color: #999;
  font-size: 12px;
  line-height: 26px;
  font-family: Source Han Sans CN;
  font-weight: 400;
}
.mainHeader {
  padding: 0 80px;
  color: #111111;
  font-size: 13px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  height: 100%;
}

.menudiv {
  display: inline-block;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  margin-left: 0;
  width: 821px;
  margin-left: 66px;
}
.wxcode {
  position: absolute;
  left: -103px;
  top: 65px;
  width: 244px;
  height: 143px;
  background: #ffffff;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.21);
  display: none;
  z-index: 100;
  .barcode {
    width: 106px;
    height: 106px;
    margin: 8px;
  }
}
.scan {
  text-align: center;
  font-size: 12px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #666666;
  width: 50%;
  float: left;
}

.wxcodeimg:hover .wxcode {
  display: block;
}
.wxcodeimg {
  position: relative;
  margin-right: 57px;
}
.active {
  background: rgba(0, 0, 0, 0.2);
}
.headerBg {
  width: 100%;
  height: 58px;
  background: #fff;
  filter: progid:DXImageTransform.Microsoft.Shadow(color=#909090,direction=120,strength=4);
  -moz-box-shadow: 2px 2px 10px #f1f1f1;
  -webkit-box-shadow: 2px 2px 10px #f1f1f1;
  box-shadow: 1px 1px 10px #f1f1f1;
  background: #ffffff;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.08);
  .main {
    color: #111111;
    font-size: 13px;
    .user {
      margin-top: 5px;
      margin-left: 550px;
      display: flex;
      width: 73px;
      font-size: 13px;
      color: rgba(51, 51, 51, 1);
      :nth-child(1) {
        margin-right: 8px;
      }
      :nth-child(2) {
        margin-right: 8px;
      }
    }
  }
}
.textheader {
  width: 100%;
  height: 26px;
  color: #2f3a4c;
  border-top: 4px solid #0b3450;
  background: -moz-linear-gradient(top, #e6e6e6 0%, #ffffff 100%);
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #e6e6e6),
    color-stop(100%, #ffffff)
  );
  background: -webkit-linear-gradient(top, #e6e6e6 0%, #ffffff 100%);
  background: -o-linear-gradient(top, #e6e6e6 0%, #ffffff 100%);
  background: -ms-linear-gradient(top, #e6e6e6 0%, #ffffff 100%);
  background: linear-gradient(to bottom, #e6e6e6 0%, #ffffff 100%);
}
.textheader .first {
  color: #2f3a4c;
  float: left;
  margin-top: 7px;
  font-size: 13px;
  font-family: Source Han Sans CN;
  font-weight: 400;
}
.textheader .second {
  float: right;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.touxiang {
  float: right;
  overflow: hidden;
  height: 38px;
  margin-top: 4px;

  .touxiangPic {
    float: left;
    margin-right: 10px;
    height: 38px;
    width: 38px;
    overflow: hidden;
    border-radius: 50%;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .loginName {
    float: left;
    height: 38px;
    line-height: 42px;
    color: #222222;
    font-size: 14px;
    overflow: hidden;
    cursor: pointer;
  }
}
.login {
  width: 340px;
  .loginTit {
    overflow: hidden;
    margin: 40px 40px;
    text-align: center;
    :nth-child(1) {
      width: 40px;
      height: 1px;
      background: rgba(204, 204, 204, 1);
      float: left;
      margin-top: 10px;
    }
    :nth-child(2) {
      font-size: 18px;
      font-weight: bold;
      color: rgba(51, 51, 51, 1);
      float: left;
      margin: 0 20px;
    }
    :nth-child(3) {
      width: 40px;
      height: 1px;
      background: rgba(204, 204, 204, 1);
      float: left;
      margin-top: 10px;
    }
  }
  .loginBut {
    width: 340px;
    height: 42px;
    background: #ed7227;
    font-size: 16px;
    color: rgba(255, 255, 255, 1);
    line-height: 42px;
    text-align: center;
    margin-top: 40px;
    margin-bottom: 30px;
    cursor: pointer;
  }
  .loginTip {
    margin: 20px 0 0;
    font-size: 14px;
    color: rgba(51, 51, 51, 1);
    span:nth-child(2) {
      font-size: 14px;
      color: #386cfc;
      cursor: pointer;
    }
    span:nth-child(3) {
      margin-left: 139px;
      cursor: pointer;
      font-size: 14px;
      color: rgba(153, 153, 153, 1);
    }
  }
  .line {
    width: 360px;
    height: 1px;
    background: rgba(238, 238, 238, 1);
    margin: 70px 0 30px;
  }
  .wechat {
    margin: 0 auto;
    font-size: 14px;
    color: rgba(51, 51, 51, 1);
    text-align: center;
    div {
      margin-bottom: 30px;
    }
    img {
      width: 26px;
      height: 26px;
      margin-bottom: 30px;
    }
  }
  .phonesend {
    width: 84px;
    height: 40px;
    background: #386cfc;
    font-size: 14px;
    color: #fff;
    text-align: center;
    line-height: 38px;
    position: absolute;
    top: 0;
    right: 0 !important;
    cursor: pointer;
  }
}
.qrImg {
  width: 282px;
  height: 339px;
  margin-left: 40px;
}
.el-menu {
  width: 100%;
  display: flex;
}
.el-menu--horizontal > .el-menu-item {
  height: 60px;
  line-height: 60px;
}
</style>
<style lang="scss">
.login {
  .el-input {
    width: 276px !important;
  }
  .el-form-item__label {
    text-align: left;
    padding: 0 !important;
    flex-shrink: 0;
  }
  .el-form-item__content {
    margin-left: 0 !important;
  }
}
</style>
<style>
.el-menu--horizontal > .el-menu-item.is-active {
  border-bottom: none !important;
  color: #386cfc !important;
  font-weight: 500 !important;
  position: relative;
}

.el-menu--horizontal > .el-menu-item.is-active::after {
  content: '';
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background-color: #386cfc;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.el-menu--horizontal > .el-menu-item {
  transition: color 0.3s ease !important;
  border-bottom: none !important;
}

.el-menu--horizontal > .el-submenu.is-active .el-submenu__title {
  border-bottom: none !important;
  color: #386cfc !important;
  font-weight: 500 !important;
  position: relative;
}

.el-menu--horizontal > .el-submenu.is-active .el-submenu__title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background-color: #386cfc;
  border-radius: 5px;
  transition: all 0.3s ease;
}

/* 未选中状态的样式 */
.el-menu--horizontal > .el-menu-item {
  font-family: Source Han Sans CN, Source Han Sans CN !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  color: #2d2f33 !important;
}

/* 鼠标悬停状态的样式 */
.el-menu--horizontal > .el-menu-item:hover {
  color: #386cfc !important; /* 悬停时的文字颜色 */
  background: #fff !important;
}
.el-menu--horizontal > .el-menu-item {
  padding: 0 30px !important;
  margin: 0 5px !important;
}

.el-menu.el-menu--horizontal {
  border-bottom: 0 !important;
}

.el-menu--horizontal {
  white-space: nowrap !important;
  overflow-x: auto !important; /* 允许横向滚动 */
}

.el-menu--horizontal::-webkit-scrollbar {
  display: none; /* 隐藏滚动条 */
}

.el-menu--horizontal {
  white-space: nowrap !important;
  overflow-x: auto !important;
}

.el-menu--horizontal::-webkit-scrollbar {
  display: none;
}
</style>