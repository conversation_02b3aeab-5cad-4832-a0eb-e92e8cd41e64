<template>
  <div class="page-layout">
    <HeaderNav :isTransparent="shouldOverlap" :isSpecialRoute="isSpecialRoute" />
    <div class="content-container" :class="{ 'no-padding': shouldOverlap }">
      <RouterView />
    </div>
    <FooterSection />
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'
import HeaderNav from '@/components/HeaderNav.vue'
import FooterSection from '@/components/FooterSection.vue'

const route = useRoute()
const shouldOverlap = ref(false)
const isSpecialRoute = ref(false)
const overlappingPages = [
  '/site/home',
  '/site/results',
  '/site/news',
  '/site/teacher',
  '/site/textbook',
  '/site/textbookBuild',
  '/site/CRM',
  '/site/university',
  '/site/majorDetail',
  '/site/universityDetail',
  '/site/classDetail',
  '/site/classStudy',
]
const specialRoutes = [
  '/site/majorDetail',
  '/site/universityDetail',
  '/site/classDetail',
  '/site/classStudy',
]
function updateHeaderState(path) {
  shouldOverlap.value = overlappingPages.includes(path)
  isSpecialRoute.value = specialRoutes.includes(path)
}
onMounted(() => {
  updateHeaderState(route.path)
})

watch(
  () => route.path,
  (newPath) => {
    updateHeaderState(newPath)
  }
)
</script>

<style lang="scss" scoped>
.top-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  height: 3rem;
  width: 100%;
  padding: 0 2rem;
}
.container {
  color: white;
  font-weight: 600;

  .header-gap {
    background-color: #d5d5d5;
    transform: rotate(45deg);
    transform-origin: 50% 50%;
    height: 0.5rem;
    width: 9rem;
  }
}

.page-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-container {
  flex: 1;
  // padding-top: 72px;
}

.no-padding {
  padding-top: 0;
}
</style>
