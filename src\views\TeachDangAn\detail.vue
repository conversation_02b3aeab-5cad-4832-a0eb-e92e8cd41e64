<template>
    <div class="wid1160">
        <back>返回</back>
        <div class="hua">
            <p class="jiao">{{ tname || (user.nickname || '我') }}的教学档案袋</p>
            <div class="bottomdiv">
              <div class="ptop">
                  <p class="pcss">濮阳职业技术学院</p>
              </div>
              <p class="share">
                  <i class="el-icon-view icss"></i>
                  <span class="scss">14</span>
                  <img @click="copyLink" class="fen" src="../../assets/cfen.svg" alt="" />
                  <span class="scss">分享</span>
              </p>
            </div>
            
        </div>

        <div>
            <p class="jtitle">
                教师档案袋
                <span class="gong" v-if="showS">是否公开</span>
                <el-switch
                v-if="showS"
                :active-value="1"
                :inactive-value="2"
                @change="changes"
                v-model="isPublic"
                active-color="#13ce66"
                inactive-color="#dcdfe6"
                >
                </el-switch>
            </p>
            <div class="cardd">
                <el-card class="wcard">
                    <template #header >
                        <span class="wan texec">完成度</span>
                        <el-progress class="progresscss" :percentage="percentage"></el-progress>
                    </template>
                    <div class="ntext">
                        <span
                        ><span class="wnum">{{ tongInfo.finishNum }}</span
                        >/7</span
                        >
                    </div>
                </el-card>
                <el-card class="wcard">
                <template #header >
                    <span class="texec">教师档案袋公开数量</span>
                </template>
                <div class="ntext">
                    <span class="ncss">{{ tongInfo.publicNum }}</span>
                </div>
                </el-card>
                <el-card class="wcard">
                <template #header>
                    <span class="texec">教师档案袋创建数量</span>
                </template>
                <div class="ntext">
                    <span class="ncss">{{ tongInfo.createNum }}</span>
                </div>
                </el-card>
            </div>
        </div>

        <div class="tabs">
        <div
            class="tabitem"
            :class="{ seltext: curItem.id == item.id }"
            v-for="item in tabs"
            :key="item.id"
            @click="clickItem(item)"
        >
            {{ item.name }}
            <div class="sline" :class="{ seline: curItem.id == item.id }"></div>
        </div>
        </div>
        <component v-if="curItem" :is="curItem.component"></component>
    </div>
</template>

<script setup>
import offercourses from './offercourses.vue'
import thesisreport from './thesisreport.vue'
import reformproject from './reformproject.vue'
import teachmaterial from './teachmaterial.vue'
import awardhonor from './awardhonor.vue'
import furtherstudy from './furtherstudy.vue'
import other from './other.vue'

import { teacherList, statisticsMap } from '@/api/index.js'
import {
  saveInvite,
  getListAll,
  getPersonInf,
  authentication,
  queryListAll,
  queryListAllMajor,
  getEaruserinfo,
} from '@/api/earuser.js'
import { computed, nextTick, onMounted, reactive, useTemplateRef } from 'vue'
import { useRoute,useRouter } from 'vue-router'
import { userStore } from "@/stores/user";
const route = useRoute()
const router = useRouter()
const user = userStore()
console.log('user',user.userType);
const isPublic = ref(2)
const tname = ref('')
const showS = ref(false)
const tabs = ref([
    {
        id: 1,
        name: '开设课程',
        component: offercourses,
        ref: 'offercourses',
    },
    {
        id: 2,
        name: '论文与会议报告',
        component: thesisreport,
        ref: 'thesisreport',
    },
    {
        id: 3,
        name: '教学改革项目',
        component: reformproject,
        ref: 'reformproject',
    },
    { id: 4, name: '教材', component: teachmaterial, ref: 'teachmaterial' },
    { 
      id: 5, 
      name: '奖励与荣誉', 
      component: awardhonor, 
      ref: 'awardhonor',
    },
    {
        id: 6,
        name: '培训进修',
        component: furtherstudy,
        ref: 'furtherstudy',
    },
    { id: 7, name: '其他', component: other, ref: 'other' },
])
const curItem = ref(tabs.value[0])
let tongInfo = reactive({
    createNum: 0,
    finishNum: 0,
    publicNum: 0,
})
const percentage = ref(0)
function getEaruserinfoApi() {
    getEaruserinfo(route.query.id).then((res) => {
        isPublic.value = res.data.isPublic
      })
}
function changes () {
      saveInvite({ isPublic: isPublic == 2 ? 2 : 1 }).then((res) => {
        if (res.status == 0) {
          ElMessage({
            type:'success',
            message:'操作成功'
          })
        }
      })
}
function statisticsMapApi() {
    statisticsMap({ createBy: route.query.id }).then((res) => {
        if (res.status == 0) {
            tongInfo = res.data
            percentage.value = parseInt((tongInfo.finishNum / 7) * 100)
        }
    })
}
function copyLink() {
    let url = location.href
    let inputNode = document.createElement('input') // 创建input
    inputNode.value = url // 赋值给 input 值
    document.body.appendChild(inputNode) // 插入进去
    inputNode.select() // 选择对象
    document.execCommand('Copy') // 原生调用执行浏览器复制命令
    inputNode.className = 'oInput'
    inputNode.style.display = 'none' // 隐藏
    ElMessage({
        type:'success',
        message:'复制成功'
    })
}

function clickItem(data) {
    curItem.value = data
}
onMounted(()=>{
    tname.value = route.query.name
    if (localStorage.getItem('token')) {
        if (localStorage.getItem('id') == route.query.id) {
            showS.value = true
            getEaruserinfoApi()
        }
    }
    if (route.query.isOne) {
    }
    statisticsMapApi()
})
</script>

<style lang="scss" scoped>
.progresscss{
  margin-left:10px;
  height: 25px;
  width: calc(100% - 60px)
}
.bottomdiv{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 60px;
}
.backimg {
  display: inline-block;
  vertical-align: bottom;
  position: relative;
  top: 1px;
}
.back {
  margin: 12px 0;
  cursor: pointer;
}
.gong {
  margin-left: 60px;
  margin-right: 10px;
}
.texec {
  font-size: 16px;
}
.ncss {
  text-align: center;
  font-size: 24px;
}
.wnum {
  font-size: 24px;
}
.ntext {
  text-align: center;
  height: 50px;
  line-height: 50px;
}
.cardd {
  margin: 20px 0px;
  display: flex;
}
.wcard {
  width: 300px;
  margin-right: 40px;
}
::v-deep(.el-card__header) {
    width: 100%;
    display: flex !important;
}
.jtitle {
  background: #ececec;
  border-radius: 4px;
  line-height: 40px;
  padding-left: 20px;
}
.scss {
  font-size: 16px;
}
.icss {
  font-size: 16px;
  margin-right: 12px;
}
.share {
  font-size: 20px;
  color: #fff;
}
.fen {
  width: 16px;
  margin: 0 12px;
  cursor: pointer;
}
.pcss {
  color: #fff;
  height: 20px;
  line-height: 20px;
  font-size: 14px;
}
.ptop {

}
.jiao {
  text-align: center;
  font-size: 20px;
  color: #fff;
  margin-top: 40px;
}
.hua {
//   height: 180px;
  background: #3283fe;
  border-radius: 8px;
  padding: 20px;
  margin-top: 12px;
  margin-bottom: 20px;
  overflow: hidden;
  position: relative;
}
.tabitem {
  height: 45px;
  color: #666666;
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  font-family: Source Han Sans CN;
  line-height: 40px;
  padding: 0 16px;
  cursor: pointer;
}
.seltext {
  color: #ed7227 !important;
}
.sline {
  width: 24px;
  height: 3px;
  margin: 0 auto;
}
.seline {
  background-color: #ed7227 !important;
}
.tabs {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.wid1160 {
  width: 1160px;
  margin: 0 auto;
  padding: 10px 0;
  height: calc(100% - 50px);
}
</style>
