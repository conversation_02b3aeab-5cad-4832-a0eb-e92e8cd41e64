<template>
    <div>
        <div class="navbox">
            <div 
                v-for="(tab, index) in tabs" 
                :key="tab.id"
                :class="['navItem', { 'active': activeTab == index }]"
                @click="setActiveTab(index)"
            >
                <div class="navText">{{ tab.title }}</div>
            </div>
        </div>
        
        <component :is="currentComponent" />
    </div>
</template>

<script setup>

const props = defineProps({
    tabs: {
        type: Array,
        required: true
    }
});

const activeTab = ref(0);

const setActiveTab = (index) => {
    activeTab.value = index;
};

const currentComponent = computed(() => {
    return props.tabs[activeTab.value].component;
});
</script>

<style lang="scss" scoped>
.navbox{
    height: 108px;
    background: rgba(255,255,255,0.72);
    border-radius: 12px 12px 12px 12px;
    border: 2px solid #FFFFFF;
    display: flex;
    align-items: center;
    margin-top: 30px;
    margin-bottom: 31px;
}

.navItem{
    min-width: 134px;
    height: 99px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 40px;
    cursor: pointer;
    transition: all 0.3s;
    
    &.active {
        background: url('@/assets/cloud/huan.png') no-repeat center center;
        background-size: 100% 100%;
        .navText{
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: bold;
            font-size: 24px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(180.00000000000006deg, #8163EE 0%, #1872FF 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
        }
    }
}

.navText{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 24px;
    color: #2A2D33;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-top: 20px;
    margin-left: 30px;
}


</style>