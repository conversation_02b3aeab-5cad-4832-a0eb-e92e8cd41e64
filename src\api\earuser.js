import service from '@/utils/request.js'

export function getEaruserinfo(id) {
  return service.request({
    method: 'get',
    url: `/ear/portal/earuserinfo/info/${id}`,
  });
}
export function saveInvite(data) {
  return service.request({
    method: 'post',
    url: `/aigc/business/userlogin/update`,
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function queryListAllMajor(data) {
  return service.request({
    method: 'get',
    url: `/ear/portal/tagitem/queryVoList`,
    params: data
  });
}

export function queryListAll(data) {
  return service.request({
    method: 'get',
    url: `/ear/portal/tagtype/queryVoList`,
    params: data
  });
}

export function getListAll(data) {
  return service.request({
    method: 'get',
    url: `/ear/portal/dictionaries/listAll`,
    params: data
  });
}
export function getPersonInf(id) {
  return service.request({
    method: 'get',
    url: `/ear/portal/earuserinfo/info/${id}`,
  });
}

export function changeUserlogin(data) {
  return service.request({
    method: 'post',
    url: `/ear/business/userlogin/updatePassword`,
    data
  });
}

export function authentication(data) {
  return service.request({
    method: 'post',
    url: `/authentication`,
    data
  });
}