<template>
  <div>
    <span class="backcss" @click="back">
      <img src="@/assets/backicon.png" alt="">
      <span class="backtext">
        <slot>

        </slot>
      </span>
    </span>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const back = () => {
  router.go(-1)
};
</script>

<style lang="scss" scoped>
.backcss{
  display: flex;
  align-items: center;
  min-width: 50px;
}
.backtext{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #386CFC ;
  text-align: left;
  font-style: normal;
  text-transform: none;
  cursor: pointer;
}
</style>