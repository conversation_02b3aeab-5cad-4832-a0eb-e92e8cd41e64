<template>
  <div class="empty-state">
    <img :src="emptyImage" :alt="alt" class="empty-image">
  </div>
</template>

<script setup>
const props = defineProps({
    emptyImage: {
      type: String,
      default: '/assets/nodataSize.png' 
    },
    alt: {
      type: String,
      default: '暂无数据'  // 或其他默认值
    }
})
</script>

<style lang="scss" scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style> 