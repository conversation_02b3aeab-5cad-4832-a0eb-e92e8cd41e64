<template>
  <div class="admin-center">
    <!-- 导航栏 -->
    <header class="header">
      <div class="logo-container">
        <img src="@/assets/cloud/delogo.png" alt="Logo" class="logo" />
      </div>
      <div class="backBox" @click="backToUniversity">
        <img src="@/assets/cloud/back.png" alt="" />
        <span class="backText">返回企业大学</span>
      </div>
    </header>

    <!-- 内容区域 -->
    <div class="content-container">
      <!-- 左侧菜单 -->
      <div class="sidebar">
        <el-menu :default-active="activeMenu" class="menu" router>
          <el-menu-item index="/admin-center/dashboard">
            <i class="iconfont icon-home"></i>
            <span>首页</span>
          </el-menu-item>
          <el-menu-item index="/admin-center/users">
            <i class="iconfont icon-user"></i>
            <span>用户管理</span>
          </el-menu-item>
          <el-menu-item index="/admin-center/courses">
            <i class="iconfont icon-zichan"></i>
            <span>课程资产</span>
          </el-menu-item>
          <el-menu-item index="">
            <i class="iconfont icon-resource"></i>
            <span>课程资源库</span>
          </el-menu-item>

          <el-menu-item index="/admin-center/statistics">
            <i class="iconfont icon-jilu"></i>
            <span>学习记录与数据分析</span>
          </el-menu-item>
          <el-menu-item index="/admin-center/settings">
            <i class="iconfont icon-system"></i>
            <span>系统管理</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 右侧内容 -->
      <div class="main-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()

const router = useRouter()
const activeMenu = computed(() => {
  return route.path
})

function backToUniversity() {
  window.close()
}
</script>

<style lang="scss" scoped>
.admin-center {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header {
  display: flex;
  align-items: center;
  height: 64px;
  background: #fff;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  height: 40px;
}

.backBox {
  display: flex;
  align-items: center;
  gap: 4px;
  .backText {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #545a66;
  }
  cursor: pointer;
  margin-left: 23px;
}

.content-container {
  display: flex;
  flex: 1;
}

.sidebar {
  width: 232px;
  background: #fff;
  border-right: 1px solid #e6e6e6;
}

.menu {
  border-right: none;
  margin: 12px;
  margin-top: 20px;
}

.main-content {
  flex: 1;
}
:deep() {
  .el-menu-item {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #545a66;
    margin-bottom: 8px;
  }
  .el-menu-item:hover {
    background: transparent;
    background-image: linear-gradient(
      91deg,
      rgba(17, 92, 255, 0.12) 23.076923%,
      rgba(154, 104, 255, 0.12) 87.980771%
    );
    border-radius: 8px;
  }
  .el-menu-item.is-active {
    background-image: linear-gradient(
      91deg,
      rgba(17, 92, 255, 0.12) 23.076923%,
      rgba(154, 104, 255, 0.12) 87.980771%
    );
    border-radius: 8px;

    span {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      background: linear-gradient(
        1.1205755698169716e-7deg,
        #115cff 23%,
        #9a68ff 88%
      );
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .iconfont {
    margin-right: 5px;
    font-size: 24px;
  }

  .el-menu-item.is-active .iconfont {
    color: #115cff;
  }
}
</style> 
