<template>
    <div class="search-input">
        <el-input v-model="searchText" placeholder="请输入内容" class="input" clearable />
        <el-button type="primary" :icon="Search" class="btn">搜索</el-button>
    </div>
</template>

<script setup>
import { ref } from 'vue'
const searchText = ref('')
import { Search } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.search-input{
  display: flex;
  align-items: center;
  height: 44px;
}
.input{
  width: 468px;
  height: 100%;
  border-radius: 8px;
  border: none;
}
.input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px transparent;
  border-radius:  8px 0 0 8px;
  background: linear-gradient(#fff, #fff) padding-box,
              linear-gradient(90deg, rgba(17, 92, 255, 1), rgba(154, 104, 255, 1)) border-box;
  border: 1px solid transparent;
}
.btn{
  width: 92px;
  height: 100%;
  background: linear-gradient( 90deg, #115CFF 23%, #9A68FF 88%);
  border-radius: 0 8px 8px 0;
  border: none;
  font-family: Source <PERSON>, Source <PERSON>N;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  cursor: pointer;
}
</style>