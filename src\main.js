import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import Back from '@/components/back.vue'
import 'element-plus/theme-chalk/display.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import ElementPlus from 'element-plus'
zhCn.el.pagination.goto = '前往'
zhCn.el.pagination.pageClassifier = '页'
import './static/element.scss'
import './static/main.css'
import './assets/styles/global.scss'
import './styles/public.scss'
import pinia from '@/stores'

// 引入阿里巴巴iconfont
import './assets/iconfont/iconfont.css'
const app = createApp(App)
app.use(ElementPlus, {
    locale: zhCn  // 一定要加上，不然无效
})
// 全局组件挂载
app.component('Back', Back)
app.use(router)
app.use(pinia)
app.mount('#app')