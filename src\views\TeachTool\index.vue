<template>
    <div class="mdivcss">
      <div class="bgcss">
        <div class="main123">
            <div class="leftMenu">
              <div 
                v-for="(item, index) in menuItems" 
                :key="index"
                :class="['menuItem', { menuItemActive: currentMenu === index }]"
              >
                <img :src="item.icon" alt="" class="menuItemImg"> 
                {{ item.name }}
              </div>
            </div>
            <div class="rightContent">
              <div class="mainContent">
                <div class="mainTitle">热门推荐</div>
                <div class="gridContainer">
                  <div class="aiToolItem" v-for="(tool, index) in aiTools" :key="index" @click="handleToolClick(tool.url)">
                    <div class="mt20">
                      <div class="aiToolItemTop">
                        <img :src="tool.icon" alt="" class="aiToolImg">
                        <div class="aiToolText">{{ tool.name }}</div>
                      </div>
                      <div class="aiToolItemBottom">
                        <div class="bottomtext">{{ tool.description }}</div>
                      </div>
                    </div>
                    <div class="Tutorial">
                        <div class="text">使用教程</div>
                        <img src="@/assets/right.png" alt="">
                    </div>
                  </div>
                </div>
              </div>
              <div class="mainContent">
                <div class="mainTitle">AI写作</div>
                <div class="gridContainer">
                  <div class="aiToolItem" v-for="(tool, index) in secondaiTools" :key="index" @click="handleToolClick(tool.url)">
                    <div class="mt20">
                      <div class="aiToolItemTop">
                        <img :src="tool.icon" alt="" class="aiToolImg">
                        <div class="aiToolText">{{ tool.name }}</div>
                      </div>
                      <div class="aiToolItemBottom">
                        <div class="bottomtext">{{ tool.description }}</div>
                        
                      </div>
                      
                    </div>
                    <div class="Tutorial">
                        <div class="text">使用教程</div>
                        <img src="@/assets/right.png" alt="">
                    </div>
                  </div>
                </div>
              </div>
            </div>
        </div>
      </div>
    </div>
</template>

<script setup>
import { menuItems,aiTools,secondaiTools } from "./data";
const currentMenu = ref(0)
const handleToolClick = (url) =>{
    if (url) {
        window.open(url, '_blank')
      }
}
</script>

 <style lang="scss" scoped>
  .wid{
    width: 1280px;
  }
  .bgcss{
    margin: 0px auto;
  }
  .mdivcss{
    padding: 20px 32px;
    padding-top: 0;
    background: linear-gradient( 90deg, #F5F8FC 0%, #F7F5FC 100%);
    min-height: calc(100vh - 320px);
  }
  .mainTitle{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #2D2F33;
  margin-bottom: 16px;
}
.mainContent{
  margin-top: 34px;
}
.aiToolItem{
  min-height: 172px;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0px 2px 20px 0px rgba(56,108,252,0.16);
    cursor: pointer;

    .aiToolText {
      color: #386CF8;
      border-bottom: 2px solid #386CF8;
    }
  }
}
.topicImg{
  max-width: 305px;
  height: 172px;
  box-shadow: 0px 2px 20px 0px rgba(56,108,252,0.16);
  border-radius: 8px 8px 8px 8px;
}
.contentTitle{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #2D2F33;
  margin-top: 12px;
  margin-bottom: 8px;
}
.contentName{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #878D99;
}


.aiToolItemTop{
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}
.aiToolImg{
  width: 28px;
  height: 28px;
}
.biaoqian{
  display: flex;
  gap: 10px;
}

.biaoqianItem{
  height: 20px;
  line-height: 20px;
  background: #EFF5FA;
  border-radius: 4px 4px 4px 4px;
  padding: 0 6px;
}
.biaoqiantext{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #9DA4B2;
}
.bottomtext{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #878D99;
  line-height: 20px;
  height: 60px;
   display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 限制行数为3 */
  overflow: hidden;
}
.mt20{
  margin: 20px;
}
.aiToolText{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #2D2F33;
}
.videoCard {
  position: relative;
}

.videoTag {
  position: absolute;
  top: 0;
  left: 0;
  width: 50px;
  height: 27px;
  line-height: 27px;
  text-align: center;
  background: #FF9B2F;
  border-radius: 8px 0px 12px 0px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 13px;
  color: #FFFFFF;
}

.main123 {
  display: flex;
  margin: 0 auto;
}

.leftMenu {
  width: 200px;
  padding: 20px 0;
  border-radius: 8px;
  margin-right: 20px;
}

.menuItem {
  padding: 16px 13px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #878D99;
}
.menuItemActive{
    border-radius: 8px 8px 8px 8px;
    background: #386CFC;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #FFFFFF;
}

.rightContent {
  flex: 1;
}

.gridContainer {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  width: 100%;
}

.aiToolItem {
  width: auto;
}
.menuItemImg{
    width: 16px;
    height: 16px;
    margin-right: 11px;
}       
.Tutorial{
    position: relative;
    left: 16px;
    bottom: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    padding: 6px;
    background: #EFF5FA;
    border-radius: 4px 4px 4px 4px;
    transition: all 0.3s ease;

    &:hover {
      background: #386CF8;

      .text {
        color: #FFFFFF;
      }

      img {
        content: url('../../assets/actoveright.png');
      }
    }
}
.text {
    margin-right: 4px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #9DA4B2;
}
</style>