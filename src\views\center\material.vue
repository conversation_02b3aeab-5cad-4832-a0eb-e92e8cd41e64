<template>
  <div class="mianbg">
    <div class="titlediv">
      <div class="title">我的教材</div>
      <el-button class="defbtn" :icon="Plus" @click="showAdd">新增教材</el-button>
    </div>
    <el-form v-if="type == 1" class="searchdiv" inline>
      <el-form-item>
        <el-input 
        class="definput" 
        placeholder="请输入教材名称"
        clearable
        v-model="pageBean.name"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input 
        class="definput" 
        placeholder="请输入主编"
        clearable
        v-model="pageBean.organizer"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input 
        class="definput" 
        placeholder="请输入出版社"
        clearable
        v-model="pageBean.organizer"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button class="defbtn" :icon="Search" @click="searchAction">搜索</el-button>
      </el-form-item>
    </el-form> 
    <div class="listdiv" :class="{mt20:type == 2}">
      <matitem 
      v-for="item in 8" 
      :key="item"
      @reload="reloadAction"></matitem>
    </div>
    <el-pagination
      v-model:current-page="pageBean.pageNum"
      v-model:page-size="pageBean.pageSize"
      :background="true"
      layout="total, prev, pager, next, jumper"
      :total="total"
      @current-change="handleCurrentChange"
      style="margin-top: 20px; justify-content: center;"/>
      <addmat 
      ref="addmaterial"
      @reload="reloadAction"></addmat>
  </div>
</template>

<script setup>
import {
  Plus,
  Search
} from '@element-plus/icons-vue'
import { 
  getMyBookList, 
  saveMaterial, 
  getMaterialDetail, 
  updateMaterial, deleteMaterial } from '@/api/expert/index.js'
import { onMounted, reactive } from 'vue';
import matitem from './common/matitem.vue';
import addmat from './common/addmat.vue';
import { ElMessage } from 'element-plus';
const pageBean = reactive({
  pageNum:1,
  pageSize:6,
  name:'',
  organizer:'',
  isMyTeach:true,
  createBy:localStorage.getItem('id')
})
const props = defineProps({
  type:{
    type:Number,
    default:1
  }
})
const total = ref(0)
const tableData = ref([])
const addmaterial = ref()

const loadData = () =>{
  getMyBookList(pageBean).then(res => {
      tableData.value = res.data
      total.value = res.page.total
  })
}
const searchAction = () =>{
  pageBean.pageNum = 1;
  loadData()
}
const handleCurrentChange = (page) =>{
  pageBean.pageNum = page;
  loadData()
}
const showAdd = () =>{
  addmaterial.value.show()
}
const reloadAction = ()=>{
  pageBean.pageNum = 1;
  loadData()
}
onMounted(()=>{
  loadData()
})
</script>

<style lang="scss" scoped>
.mt20{
  margin-top: 20px;
}
.titlediv{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #2D2F33;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.searchdiv{
  margin-top: 20px;
  ::v-deep(.el-form-item){
    margin-right: 12px;
  }
}
.definput{
  width: 200px;
  height: 36px;
  border-radius: 4px;
  :v-deep(.el-input__inner){
    border: 1px solid #E1E4EB;
  }
}
.listdiv{
  display: flex;
  flex-wrap: wrap;
  .matitem{
    width: calc(25% - 45px);
    margin-right: 60px;
  }
  .matitem:nth-child(4n){
    margin-right: 0px !important;
  }
}

.topBtn{
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>