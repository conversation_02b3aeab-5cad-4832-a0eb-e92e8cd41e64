export const getFileTypeResult = (fileName) => {
    let suffix = ''; // 后缀获取
    let result = ''; // 获取类型结果
    if (fileName) {
      const flieArr = fileName.split('.'); // 根据.分割数组
      suffix = flieArr[flieArr.length - 1]; // 取最后一个
    }
    if (!suffix) return false; // fileName无后缀返回false
    suffix = suffix.toLocaleLowerCase(); // 将后缀所有字母改为小写方便操作
    // 匹配图片
    const imgList = ['png', 'jpg', 'jpeg', 'bmp', 'gif']; // 图片格式
    result = imgList.find(item => item === suffix);
    if (result) return '1';
    // 匹配excel
    const excelList = ['xls', 'xlsx', 'doc', 'docx', 'pdf', 'ppt', 'pptx', 'rar', 'zip'];
    result = excelList.find(item => item === suffix);
    if (result) return '4';
    const videoList = ['mp4', 'm2v', 'mkv', 'rmvb', 'wmv', 'avi', 'flv', 'mov', 'm4v'];
    result = videoList.find(item => item === suffix);
    if (result) return '2';
    // 匹配音频
    const radioList = ['mp3', 'wav', 'wmv'];
    result = radioList.find(item => item === suffix);
    if (result) return '3';
    // 其他文件类型
    return 'other';
  }