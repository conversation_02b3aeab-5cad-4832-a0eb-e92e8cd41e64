<svg width="68" height="68" viewBox="0 0 68 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 4881">
<g id="Rectangle 34624316" filter="url(#filter0_di_1795_35)">
<rect x="6" y="2" width="56" height="56" rx="22" fill="url(#paint0_linear_1795_35)"/>
</g>
<g id="Vector" filter="url(#filter1_d_1795_35)">
<path d="M27.3462 15H22.5769C20.5977 15 19 16.5977 19 18.5769V37.6538C19 39.6331 20.5977 41.2308 22.5769 41.2308H27.3462C29.3254 41.2308 30.9231 42.8285 30.9231 44.8077C30.9231 45.4754 31.4477 46 32.1154 46C32.7831 46 33.3077 45.4754 33.3077 44.8077V20.9615C33.3077 17.6708 30.6369 15 27.3462 15ZM46.4231 15H41.6538C38.3631 15 35.6923 17.6708 35.6923 20.9615V44.8077C35.6923 45.4754 36.2169 46 36.8846 46C37.5523 46 38.0769 45.4754 38.0769 44.8077C38.0769 42.8285 39.6746 41.2308 41.6538 41.2308H46.4231C48.4023 41.2308 50 39.6331 50 37.6538V18.5769C50 16.5977 48.4023 15 46.4231 15Z" fill="url(#paint1_linear_1795_35)" shape-rendering="crispEdges"/>
</g>
</g>
<defs>
<filter id="filter0_di_1795_35" x="0" y="0" width="68" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.196078 0 0 0 0 0.580392 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1795_35"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1795_35" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.196429 0 0 0 0 0.581731 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1795_35"/>
</filter>
<filter id="filter1_d_1795_35" x="15" y="13" width="39" height="39" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.196078 0 0 0 0 0.580392 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1795_35"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1795_35" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1795_35" x1="34" y1="2" x2="34" y2="58" gradientUnits="userSpaceOnUse">
<stop stop-color="#61D0FD"/>
<stop offset="1" stop-color="#3E7AE5"/>
</linearGradient>
<linearGradient id="paint1_linear_1795_35" x1="34.5" y1="15" x2="34.5" y2="46" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.9"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
