<template>
  <div>
    <div class="Self">
      <div class="picture">
        <el-upload
          class="avatar-uploader"
          :action="getUrl"
          :show-file-list="false"
          :data="imageData"
          name="file"
          :headers="headerUrl"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
        >
          <img v-if="ruleForm.logo" :src="ruleForm.logo" class="avatar" />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          <el-button><i class="el-icon-edit"></i> 编辑头像</el-button>
        </el-upload>
      </div>
      <div style="margin-top: 20px" class="information">
        <el-form ref="form" :model="ruleForm" label-width="120px">
          <el-form-item label="用户名:">
            <el-input v-model="ruleForm.name" placeholder="请输入用书名">
            </el-input>
          </el-form-item>
          <el-form-item label="姓名:">
            <el-input v-model="ruleForm.name" placeholder="请输入姓名">
            </el-input>
          </el-form-item>
          <el-form-item label="年龄:" prop="mobilephone">
            <el-input
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的年龄"
              class="mobilephone"
            ></el-input>
          </el-form-item>
          <el-form-item label="性别:">
            <el-select
              class="sexselect commonSelect"
              v-model="ruleForm.region"
              placeholder="请选择您的性别"
            >
              <el-option label="Zone one" value="shanghai" />
              <el-option label="Zone two" value="beijing" />
            </el-select>
          </el-form-item>
          <el-form-item label="单位:">
            <!-- <el-select
              class="sexselect"
              v-model="ruleForm.region"
              placeholder="请选择您的单位"
            >
              <el-option label="Zone one" value="shanghai" />
              <el-option label="Zone two" value="beijing" />
            </el-select> -->
            <el-input
              maxlength="20"
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的姓名"
              class="mobilephone"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="部门:" prop="mobilephone">
            <el-input
              maxlength="20"
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的部门"
              class="mobilephone"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="职称:" prop="mobilephone">
            <el-input
              maxlength="10"
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的职称"
              class="mobilephone"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="密码:" prop="mobilephone">
            <div class="setpassword" @click="showPassDialog">设置/更改密码</div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="saveInfo" class="submit"
              >立即保存
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <el-dialog
      v-model="passdialogVisible"
      width="493px"
      class="infoDialog"
      :show-close="false"
    >
      <template #header="{ close }">
        <div class="my-header">
          <p class="dtitle">信息补充</p>
          <img
            class="close"
            src="../../assets/close.png"
            alt=""
            @click="close"
          />
        </div>
      </template>
      <div class="infobox">
        <el-form
          :model="rulePassForm"
          :rules="rulesPass"
          ref="ruleForm"
          label-width="64px"
          label-position="top"
        >
          <el-form-item label="手机号:" prop="mobilephone">
            <el-input
              maxlength="11"
              v-model="rulePassForm.mobilephone"
              placeholder="请输入您的手机号"
              class="mobilephone"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="验证码:" prop="mobilephone">
            <el-input
              v-model="rulePassForm.mobilephone"
              placeholder="请输入您的验证码"
              class="mobilephone"
            >
              <template #suffix>
                <el-button text class="sendCode">
                  {{ codeNum == 60 ? '发送验证码' : `(${codeNum})发送验证码` }}
                </el-button></template
              ></el-input
            >
          </el-form-item>
          <el-form-item label="设置密码:" prop="mobilephone">
            <el-input
              maxlength="10"
              v-model="rulePassForm.mobilephone"
              placeholder="请输入您的密码"
              class="mobilephone"
              show-word-limit
            ></el-input>
          </el-form-item>
          <p class="tips">6-20个字符，包含数字、字母即可</p>
        </el-form>
        <el-button class="saveButton" type="primary">立即保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script setup>
import {
  saveInvite,
  authentication,
  queryListAllMajor,
} from '@/api/center/earuser.js'
import { getPersonInf } from '@/api/expert/index.js'
import { baseUrl, uploadFiles3 } from '@/config.js'
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { userStore } from '@/stores/user.js'
const getUrl = `${baseUrl}${uploadFiles3}`
const imageData = { serviceName: 'web' }
const headerUrl = { Authorization: sessionStorage.getItem('token') }
const logo = ref('')
const id = localStorage.getItem('id')
const useUserStore = userStore()
const ruleForm = ref({
  logo: '',
  imagesFileName: '',
  imagesFileSize: '',
  majorId: '',
  name: '',
  intro: '',
})
const handleAvatarSuccess = (res, file) => {
  logo.value = URL.createObjectURL(file.raw)
  ruleForm.value.logo = res.data.url
  ruleForm.value.imagesFileName = res.data.fileName
  ruleForm.value.imagesFileSize = res.data.size
}
const beforeAvatarUpload = (file) => {
  const isLt2M = file.size / 1024 / 1024 < 10
  if (!isLt2M) {
    this.$message.error('上传头像图片大小不能超过 10MB!')
    return false
  }
  return true
}

const formCode = ref({ schoolInvitationCode: '' })
const majorData = ref([])

function changeSelect(value, boo, callback) {
  majorData.value = []
  if (!boo) {
    ruleForm.value.majorId = ''
  }
  queryListAllMajor({ tagtypeId: value }).then((res) => {
    if (res.status == 0) {
      majorData.value = res.data
      if (callback) {
        callback()
      }
    }
  })
}

function isInformation() {
  getPersonInf({ id: id }).then((res) => {
    if (res.status == 0) {
      let obj = { ...res.data }
      ruleForm.value = obj
      ruleForm.value.majorId = ''
      if (res.data.collegeId) {
        changeSelect(res.data.collegeId, true, () => {
          if (res.data.majorId == '0' || res.data.majorId == 0) {
            ruleForm.value.majorId = ''
          } else {
            ruleForm.value.majorId = res.data.majorId
          }
        })
      }
      formCode.value.schoolInvitationCode = res.data.schoolInvitationCode
      ruleForm.value.logo = res.data.logo
    }
  })
}
isInformation()

function saveInfo() {
  delete ruleForm.value.modifyTime
  delete ruleForm.value.createTime
  saveInvite(ruleForm.value).then((res) => {
    if (res.status == 0) {
      useUserStore.changePicUrl(ruleForm.value.logo)
      useUserStore.changeNickName(ruleForm.value.name)
      ElMessage.success('恭喜你，保存成功！')
    } else {
      ElMessage.error('保存失败！')
    }
  })
}

const passdialogVisible = ref(false)
const rulePassForm = ref({})
const rulesPass = {
  mobilephone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  messageAuthCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
  ],
}

const showPassDialog = () => {
  passdialogVisible.value = true
}

const codeNum = ref(60)
let clearId = null
const isClickSend = ref(false)
const sendCode = async () => {
  if (isClickSend.value) return
  isClickSend.value = true
  const res = await getCode(mobile.value, 'login')
  clearId.value = setInterval(() => {
    codeNum.value--
    if (codeNum.value == 0) {
      clearInterval(clearId.value)
      codeNum.value = 60
      isClickSend.value = false
    }
  }, 1000)
}
</script>
<style lang="scss" scoped>
.sendCode {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #386cfc;
  padding: 0;
  &:hover {
    background: none !important;
  }
}
.tips {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 12px;
  color: #878d99;
}
.saveButton {
  width: 100%;
  height: 42px;
  background: #386cfc;
  border-radius: 4px 4px 4px 4px;
  line-height: 42px;
  margin-top: 22px;
}
:deep(.el-input) {
  height: 40px;
  line-height: 40px;
}
.close {
  width: 24px;
  height: 24px;
  cursor: pointer;
  position: absolute;
  right: 24px;
}
.dtitle {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #2d2f33;
  padding-left: 30px;
}
.my-header {
  position: relative;
  padding-top: 20px;
  display: flex;
  width: 493px;
  height: 71px;
  background: linear-gradient(180deg, #e7edfe 0%, rgba(231, 237, 254, 0) 100%);
  justify-content: space-between;
}
.infoDialog {
  padding: 0;
  width: 493px;
  background: #ffffff;
  box-shadow: 0px 2px 20px 0px rgba(33, 58, 128, 0.16);
  border-radius: 8px 8px 8px 8px;
  overflow: hidden;
}
.infoDialog .el-dialog__body {
  padding: 30px;
  padding-top: 10px;
}
.infoDialog .el-dialog__header {
  padding-bottom: 0px;
}
.infoDialog label {
  height: 40px;
  line-height: 40px;
}
.setpassword {
  width: 122px;
  height: 42px;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #386cfc;
  line-height: 42px;
  text-align: center;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #386cfc;
  cursor: pointer;
}
.Self {
  display: flex;
}
:deep(.el-upload) {
  display: block !important;
}
.avatar-uploader-icon {
  display: block;
}
:deep(.el-input__wrapper) {
}
.information {
  .el-select:hover .el-input__inner {
    border-color: #e6e6e6 !important;
  }
  :deep(.el-input__inner) {
    height: 40px;
    line-height: 40px;
    border-radius: 0;
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #666;
  }

  .el-textarea__inner {
    height: 120px;
    width: 317px;
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #666;
    line-height: 26px;
  }
}
.picture {
  width: 130px;
  .el-icon-plus:before {
    line-height: 130px;
  }
}
</style>
<style lang="scss" scoped>
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 130px;
  height: 130px;
}
.avatar {
  width: 130px;
  height: 130px;
  display: block;
}
.invite_input {
  height: 50px;
  background: #f9f9f9;
  line-height: 50px;
  .demo-input-suffix {
    padding-left: 20px;
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 50px;
    .el-input {
      width: 41%;
    }
    span {
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ed7227;
      line-height: 50px;
      margin-left: 68px;
      cursor: pointer;
    }
  }
}
.picture {
  margin-top: 20px;
  text-align: center;
  .el-button {
    width: 88px;
    height: 30px;
    border: 1px solid #e6e6e6;
    border-radius: 0;
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #666666;
    line-height: 28px;
    margin-top: 12px;
    padding: 0;
  }
  .avatar-uploader .el-upload .el-icon-plus {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
}
.edit {
  margin-top: 20px;
  img {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
  span {
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 16px;
    cursor: pointer;
  }
}

.submit {
  width: 160px;
  height: 42px;
  line-height: 42px;
  border: 0;
  border-radius: 4px 4px 4px 4px;
  padding: 0;
  background: #386cfc;
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  margin-top: 20px;
}
.el-button--primary:active {
  background: #386cfc;
  border-color: #386cfc;
  color: #fff;
}
</style>
