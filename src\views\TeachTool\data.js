import hot from '@/assets/hot.png'
import write from '@/assets/write.png'
import draw from '@/assets/draw.png'
import aivideo from '@/assets/aivideo.png'
import  word from '@/assets/word.png'
import ui from '@/assets/ui.png'
import chat from '@/assets/chat.png'
import mita from '@/assets/mita.png';
import tiangong from '@/assets/tiangong.png';
import wenxin from '@/assets/wenxin.png';
import kimi from '@/assets/kimi.png';
import doubao from '@/assets/doubao.png';
import tongyi from '@/assets/tongyi.png';
import keling from '@/assets/keling.png';
import jimeng from '@/assets/jimeng.png';
import deepseek from '@/assets/deepseek.png';
import biling from '@/assets/biling.png';
import huoshan from '@/assets/huoshan.png';
import xunfei from '@/assets/xunfei.png';
import Paperpal from '@/assets/Paperpal.png';
import miaobi from '@/assets/miaobi.png';
import bilingai from '@/assets/bilingai.png';
import mohuai from '@/assets/mohuai.png';
import ihuiwa from '@/assets/ihuiwa.png';
import notion from '@/assets/notion.png';
import songguoai from '@/assets/songguoai.png';
import chengpian from '@/assets/chengpian.png';
export const menuItems =  [
          {
            name: '热门推荐',
            icon: hot
          },
          {
            name: 'AI写作',
            icon: write
          },
          {
            name: 'AI绘图',
            icon: draw
          },
          {
            name: 'AI视频',
            icon: aivideo
          },
          {
            name: 'AI办公',
            icon: word
          },
          {
            name: 'AI设计',
            icon: ui
          },
          {
            name: 'AI对话',
            icon: chat
          },
        ]
export const aiTools = [
        {
          icon: mita,
          name: '秘塔',
          description: '告别广告干扰，开启高效搜索！秘塔 AI 搜索引擎，直击结果，用实力定义实用',
          url: 'https://metaso.cn'
        },
        {
          icon: tiangong,
          name: '天工',
          description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
          url: 'https://www.tiangong.cn'
        },

        {
          icon: wenxin,
          name: '文心一言',
          description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
          url: 'https://yiyan.baidu.com'
        },
        {
          icon: kimi,
          name: 'Kimi',
          description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
          url: 'https://kimi.moonshot.cn'
        },
        {
          icon: doubao,
          name: '豆包',
          description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
          url: 'https://www.doubao.com'
        },
        {
          icon:tongyi,
          name: '通义千问',
          description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
          url: 'https://tongyi.aliyun.com'
        },
        {
          icon: keling,
          name: '可灵',
          description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
          url: 'https://klingai.kuaishou.com'
        },
        {
          icon: jimeng,
          name: '即梦',
          description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
          url: 'https://jimeng.jianying.com'
        },
        {
          icon: deepseek,
          name: 'DeepSeek',
          description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
          url: 'https://www.deepseek.com'
        },
        {
          icon: biling,
          name: '笔灵AI',
          description: '笔灵AI写作助手，好的AI启发思路和灵感',
          url: 'https://ibiling.cn/template'
        },
        {
          icon: huoshan,
          name: '火山写作',
          description: '字节的AI英语写作工具，高效提升英文表达',
          url: 'https://ai-bot.cn/sites/957.html'
        },
        {
          icon: xunfei,
          name: '讯飞文书',
          description: '讯飞写作，国货之光！搭载国产大模型，是职场人撰写公文的不二之选',
          url: 'https://gw.iflydocs.com/?from=aibot'
        },
        ]
export const secondaiTools = [
        {
          icon: biling,
          name: '笔灵AI',
          description: '笔灵AI写作助手，好的AI启发思路和灵感',
          url: 'https://ibiling.cn/template'
        },
        {
          icon: huoshan,
          name: '火山写作',
          description: '字节的AI英语写作工具，高效提升英文表达',
          url: 'https://ai-bot.cn/sites/957.html'
        },

        {
          icon: Paperpal,
          name: 'Paperpal',
          description: '写作、润色功能全具备，有 AI 加持，是英语论文的专属智能助手',
          url: 'https://www.editage.cn/paperpal?utm_source=ai-bot&utm_medium=Banner&utm_campaign=Banner'
        },
        {
          icon: miaobi,
          name: '新华妙笔',
          description: '有新华妙笔助力，让公文写作变得高效又轻松，轻松告别繁琐的创作过程 ',
          url: 'https://miaobi.xinhuaskl.com/?channel=shuzhi'
        },
        {
          icon: bilingai,
          name: '笔灵AI小说',
          description: '让创作过程变得轻松又有趣，助力每一位创作者开启小小说创作的奇妙之旅',
          url: 'https://ibiling.cn/novel-workbench?from=aibotnovel'
        },
        {
          icon: xunfei,
          name: '讯飞文档',
          description: '讯飞写作，国货之光！搭载国产大模型，是职场人撰写公文的不二之选',
          url: 'https://tongyi.aliyun.com'
        },
        {
          icon: mohuai,
          name: '墨狐AI',
          description: '短篇小说AI写作助手，专为网文小说作者设计',
          url: 'https://inkfox-ai.com'
        },
        {
          icon: ihuiwa,
          name: '绘蛙AI文案',
          description: '专业文案写作工具，具备免费爆文改写功能，让你的文案瞬间吸睛',
          url: 'https://www.ihuiwa.com'
        },
        {
          icon: notion,
          name: 'Notion AI',
          description: 'Notion推出的AI内容创作助手',
          url: 'https://www.notion.com'
        },
        {
          icon: songguoai,
          name: '松果AI写作',
          description: '作为一款专业 AI 写作工具，支持批量生成文章，开启创作新世界',
          url: 'https://songguoai.com'
        },
        {
          icon: chengpian,
          name: '橙篇',
          description: '百度推出的AI长文理解和内容创作工具',
          url: 'https://cp.baidu.com'
        },
        {
          icon:xunfei,
          name: '讯飞写作',
          description: '讯飞写作，科大讯飞推出的 AI 智能写作助手，以强大功能助力高效创作',
          url: 'https://huixie.iflyrec.com'
        },
        ]