{"name": "web-site", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vueuse/core": "^13.1.0", "axios": "^1.3.5", "crypto-js": "^4.1.1", "element-plus": "^2.3.3", "moment": "^2.29.4", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "plyr": "^3.7.8", "swiper": "^11.2.7", "three": "^0.172.0", "vite-plugin-vue-devtools": "^7.7.7", "vue": "^3.2.47", "vue-awesome-swiper": "^5.0.1", "vue-router": "^4.1.6", "vue3-video-play": "1.3.1-beta.6", "xgplayer": "^3.0.22"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "rollup-plugin-visualizer": "^5.9.0", "sass": "^1.61.0", "unplugin-auto-import": "^0.15.2", "unplugin-icons": "^0.16.1", "unplugin-vue-components": "^0.24.1", "vite": "^4.1.4"}}