<template>
    <div class="types">
        <div class="flex">
            <span class="tname">{{ name }}：</span>
            <ul ref="typeul" class="typeul" :class="{ 'collapsed': !isShow && needShowMore }">
            <li 
                v-for="item in list" 
                :key="item.value"
                :class="{ isActive: modelValue === item.value }"
                @click="handleClick(item.value)"
            >
                {{ item.label }}
            </li>
            </ul>
            <span v-if="needShowMore" class="morebtn" @click="showType">
            {{isShow ? '收起':'展开'}}
            <i class="arrow-icon" :class="{ 'up': isShow }"></i>
            </span>
        </div>
    </div>
</template>

<script setup>
import shouqi from '@/assets/shouqi.png';
import zhankai from '@/assets/zhankai.png';

const isShow = computed(()=>{
    return !props.defaultCollapsed
})
const needShowMore = ref(false)
const collapsed = ref(true)
const props = defineProps({
    name: {
        type: String,
        required: true
    },
    list: {
        type: Array,
        default: () => []
    },
    modelValue: {
        type: String,
        default: ''
    },
    level: {
        type: Number,
        default: 1
    },
    defaultCollapsed: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['modelValue'])
const handleClick = (value) => {
  console.log('ddddddd',value);
    emit('update:modelValue', value)
}
const showType = () =>{
    isShow.value = !isShow.value;
    collapsed.value = !collapsed.value;
}
const typeul = ref()
const checkNeedShowMore = () => {
    nextTick(() => {
        if (typeul) {
        // 更精确地检查是否有内容溢出
        const items = typeul.value.querySelectorAll('li');
        let totalWidth = 0;
        items.forEach(item => {
            totalWidth += item.offsetWidth + parseInt(getComputedStyle(item).marginLeft) + parseInt(getComputedStyle(item).marginRight);
        });
        needShowMore.value = totalWidth > typeul.value.offsetWidth;
        }
    });
}
onMounted(()=>{
    nextTick(() => {
        checkNeedShowMore();
        window.addEventListener('resize', checkNeedShowMore);
        isShow.value = !props.defaultCollapsed;
    });
})
onBeforeUnmount(()=>{
    window.removeEventListener('resize', checkNeedShowMore);
})
</script>

<style lang="scss" scoped>
.tname {
    min-width: 100px;
    text-align: left;
    height: 48px;
    line-height: 48px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #4B4D4B;
}
.morebtn{
  width: 80px;
  height: 24px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #386CFC;
  line-height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  margin-top: 12px;
}
.types {
  width: 100%;
}

.flex {
  display: flex;
  align-items: flex-start;
}

.typeul {
  width: calc(100% - 160px);
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  min-height: 32px;
  line-height: 32px;
  margin-left: 0;
  padding-left: 0;
  max-height: none;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
  
  &.collapsed {
    max-height: 48px;
    overflow: hidden;
  }
}
.typeul li {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #4B4D4B;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin: 8px 15px;
  padding: 0px 14px;
  cursor: pointer;
  border-radius: 50px;  
  transition: all 0.3s; 
  &:hover {
    background: rgba(56,108,252,0.12) !important;
    color: #386CFC !important;
  } 
  
  &.isActive {
    background: rgba(56,108,252,0.12) !important;
    color: #386CFC !important;
  }
}

.arrow-icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-left: 4px;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 6px;
    border-right: 2px solid #386CFC;
    border-bottom: 2px solid #386CFC;
    transform: translate(-50%, -75%) rotate(45deg);
    transition: transform 0.3s;
  }
  
  &.up::after {
    transform: translate(-50%, -25%) rotate(-135deg);
  }
}
</style>