import service from '@/utils/request.js'
export function repositoryList(data) {
  return service.request({
    method: 'post',
    url: '/resource/selectAll',
    data
  });
}

export function collectList(params) {
  return service.request({
    method: 'get',
    url: '/ear/portal/resourcecollect/list',
    params,
  });
}

export function zhengList(params) {
  return service.request({
    method: 'get',
    url: '/ear/portal/resourcecertificate/list',
    params,
  });
}

export function listXueFen(params) {
  return service.request({
    method: 'get',
    url: '/ear/portal/usercreadits/list',
    params,
  });
}


export function queryHistoryList(params) {
  return service.request({
    method: 'get',
    url: '/ear/portal/resourcelearnrecord/queryHistoryList',
    params,
  });
}

export function queryStatistics(params) {
  return service.request({
    method: 'get',
    url: '/ear/portal/resourcelearnrecord/queryStatistics',
    params,
  });
}

export function queryLearningDurationList(params) {
  return service.request({
    method: 'get',
    url: '/ear/portal/onlinelearningresource/queryLearningDurationList',
    params,
  });
}


export function qunlist(params) {
  return service.request({
    method: 'get',
    url: '/ear/portal/resourcegroup/list',
    params,
  });
}
export function queryCaseCommentList(params) {
  return service.request({
    method: 'get',
    url: '/ear/portal/groupcomment/list',
    params,
  });
}

export function deletecasecomment(data) {
  return service.request({
    method: 'post',
    url: '/ear/portal/groupcomment/delete',
    data,
  });
}



export function queryStatisticsXueFen(params) {
  return service.request({
    method: 'get',
    url: '/ear/portal/usercreadits/queryStatistics',
    params,
  });
}

export function qundelete(data) {
  return service.request({
    method: 'post',
    url: '/ear/portal/resourcegroup/delete',
    data,
  });
}


export function qunSave(data) {
  return service.request({
    method: 'post',
    url: '/ear/portal/resourcegroup/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function casecommentSave(data) {
  return service.request({
    method: 'post',
    url: '/ear/portal/groupcomment/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}



export function jiaSave(data) {
  return service.request({
    method: 'post',
    url: '/ear/portal/resourcegroupuser/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function collectSave(data) {
  return service.request({
    method: 'post',
    url: '/ear/portal/resourcecollect/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function collectDelete(data) {
  return service.request({
    method: 'post',
    url: '/ear/portal/resourcecollect/delete',
    data,
  });
}

export function getMajor(data) {
  return service.request({
    method: 'get',
    url: `/goldcourse/web/specialtyclassify/list`,
    params: data
  });
}

export function login(data) {
  return service.request({
    method: 'post',
    url: `/login`,
    data,
    type: 3
  });
}

export function logout() {
  return service.request({
    method: 'get',
    url: `/logout`,
  });
}

export function verifyMobilePhone(data) {
  return service.request({
    method: 'post',
    url: `/aigc/business/userlogin/verifyMobilePhone`,
    data
  });
}


export function userRegister(data) {
  return service.request({
    method: 'post',
    url: `/register`,
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


export function registerLogin(data) {
  return service.request({
    method: 'post',
    url: `/ear/business/userlogin/registerLogin`,
    data
  });
}



export function messageAuthCodeLogin(data) {
  return service.request({
    method: 'post',
    url: `/messageAuthCodeLogin`,
    data
  });
}


export function sendMessage(data) {
  return service.request({
    method: 'post',
    url: `/aliyun/sms/sendMessage`,
    data
  });
}


export function bannerList() {
  return service.request({
    method: 'get',
    url: `/ear/portal/advertisingspace/list`,
  });
}

export function indexListLive(params) {
  return service.request({
    method: 'get',
    url: `/aigc/business/liveinfo/list`,
    params
  });
}


export function courseDetail(id) {
  return service.request({
    method: 'get',
    url: `/aigc/business/courseinfo/info/${id}`,
  });
}
export function resourceDetail(id) {
  return service.request({
    method: 'get',
    url: `/aigc/business/courseinfo/info/${id}`,
  });
}


export function courseDetailNew(id) {
  return service.request({
    method: 'get',
    url: `/ear/portal/onlinelearningresource/info/${id}`,
  });
}

export function indexData() {
  return service.request({
    method: 'get',
    url: `/ear/portal/home/<USER>
  });
}

export function indexcourselist() {
  return service.request({
    method: 'get',
    url: `/ear/portal/home/<USER>
  });
}


export function WeChatLogin(data) {
  return service.request({
    method: 'post',
    url: '/ear/business/userlogin/WeChatLogin',
    data
  });
}

export function newsList(params) {
  return service.request({
    method: 'get',
    url: `/ear/portal/afficheinfo/list`,
    params
  });
}

export function newsInfo(id) {
  return service.request({
    method: 'get',
    url: `/ear/portal/afficheinfo/info/${id}`,
  });
}


export function courseList(params) {
  return service.request({
    method: 'get',
    url: '/ear/portal/onlinelearningresource/list',
    params
  });
}


export function courseInfo(id) {
  return service.request({
    method: 'get',
    url: `/ear/portal/onlinelearningresource/info/${id}`,
  });
}
export function coursetopicList(params) {
  return service.request({
    method: 'get',
    url: '/ear/portal/coursetopic/list',
    params
  });
}
export function coursetopicInfo(id) {
  return service.request({
    method: 'get',
    url: `/ear/portal/coursetopic/info/${id}`,
  });
}


export function teacherList(params) {
  return service.request({
    method: 'get',
    url: '/ear/portal/earuserinfo/list',
    params,
  });
}

export function statisticsMap(params) {
  return service.request({
    method: 'get',
    url: '/ear/portal/filebagcourse/statisticsMap',
    params,
  });
}
