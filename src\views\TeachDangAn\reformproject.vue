<template>
  <div class="bgcss">
    <div class="headcss">
      <div class="ticon">教学改革项目</div>
      <el-button class="addbtns" v-if="isShow" size="small" @click="showAdd"
        >添加教学改革项目</el-button
      >
    </div>
    <div class="listdiv">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="name" label="项目名称"> </el-table-column>
        <el-table-column prop="projectType" label="项目类型" width="180">
        </el-table-column>
        <el-table-column prop="reportTime" label="申报时间" width="180">
        </el-table-column>
        <el-table-column
          prop="personalContribution"
          label="个人贡献"
          width="180"
        >
        </el-table-column>
        <el-table-column prop="edit" label="操作" width="180" v-if="isShow">
          <template #default="scope">
            <span class="editbtn" @click="editAction(scope.row)">编辑</span>
            |
            <span class="delbtn" @click="deleteAction(scope.row)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="bottomcss">
      <el-pagination
        :current-page="pageBean.pageNum"
        :page-size="pageBean.pageSize"
        :total="total"
        @current-change="changePage"
        background
      ></el-pagination>
    </div>
    <el-dialog
      class="mydialog"
      top="50px"
      v-model="dialogFormVisible"
      :title="dTitle"
      @close="close2"
    >
      <el-form label-width="100px" :model="form" :rules="rules" ref="myform">
        <el-form-item label="项目名称：" prop="name">
          <el-input placeholder="请输入项目名称" v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="申报时间：" prop="reportTime">
          <el-date-picker
            v-model="form.reportTime"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="项目类型：" prop="projectType">
          <el-input
            placeholder="请输入项目类型"
            v-model="form.projectType"
          ></el-input>
        </el-form-item>
        <el-form-item label="个人贡献：" prop="personalContribution">
          <el-input
            placeholder="请输入个人贡献"
            v-model="form.personalContribution"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="close2">取 消</el-button>
        <el-button type="primary" @click="submitAction">保 存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  teachingprojectList,
  teachingprojectInfo,
  teachingprojectSave,
  teachingprojectUpdate,
  teachingprojectDelete,
} from '@/api/center/index'
import { ElMention, ElMessage } from 'element-plus';
import { onMounted, reactive } from 'vue';
import { useRouter,useRoute } from 'vue-router';
const route = useRoute()
const router = useRouter()
const isShow = ref(false)
const dialogFormVisible = ref(false)
const dTitle = ref('')
const pageBean =  reactive({
  pageNum: 1,
  pageSize: 8,
})
const tableData = ref([])
const total = ref(0)
const form = reactive({
  name: '',
  projectType: '',
  personalContribution: '',
  reportTime: '',
})
const rules = ref({
    name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
    projectType: [
      { required: true, message: '请输入项目类型', trigger: 'blur' },
    ],
    personalContribution: [
      { required: true, message: '请输入个人贡献', trigger: 'blur' },
    ],
    reportTime: [
      { required: true, message: '请选择申报时间', trigger: 'change' },
    ],
})
function loaddata() {
  teachingprojectList(pageBean)
    .then((result) => {
      tableData.value = result.data
      total.value = result.page.total
    })
    .catch((err) => {})
}
function changePage(page) {
  pageBean.pageNum = page
  loaddata()
}
function showAdd() {
  dialogFormVisible.value = true
  dTitle.value = '添加教学改革项目'
}
function editAction(data) {
  teachingprojectInfo(data.id)
    .then((result) => {
      form = result.data
      delete form.createTime
      delete form.createBy
      dialogFormVisible.value = true
      dTitle.value = '编辑教学改革项目'
    })
    .catch((err) => {})
}

const myform = ref()
function close2() {
  Object.keys(form).forEach((item) => {
    form[item] = ''
  })
  myform.value.clearValidate()
  dialogFormVisible.value = false
}
function update() {
  teachingprojectUpdate(form)
    .then((result) => {
      if (result.data) {
        ElMessage.success('保存成功')
        loaddata()
        close2()
      } else {
        ElMessage.error(result.msg)
      }
    })
    .catch((err) => {})
}
function add() {
  teachingprojectSave(form)
    .then((result) => {
      if (result.data) {
        ElMessage.success('保存成功')
        loaddata()
        close2()
      } else {
        ElMessage.error(result.msg)
      }
    })
    .catch((err) => {})
}
function deleteItem(id) {
  teachingprojectDelete({
    id: id,
  })
    .then((result) => {
      if (result.data) {
        ElMessage({
          type: 'success',
          message: '删除成功!',
        })
        loaddata()
      } else {
        ElMessage({
          type: 'error',
          message: result.msg,
        })
      }
    })
    .catch((err) => {})
}
function deleteAction(data) {
  ElMessageBox.confirm('此操作将删除当前数据, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      deleteItem(data.id)
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除',
      })
    })
}
function submitAction() {
  myform.validate((valid) => {
    if (valid) {
      if (form.id) {
        update()
      } else {
        add()
      }
    } else {
      return false
    }
  })
}
onMounted(()=>{
    pageBean.createBy = route.query.id
    if (localStorage.getItem('id') == route.query.id) {
      isShow.value = true
    }
    loaddata()
})
</script>
<style lang="scss" scoped>
.editbtn {
  color: #409eff;
  cursor: pointer;
}
.delbtn {
  color: #f56c6c;
  cursor: pointer;
}
.ticon {
  font-size: 14px;
  font-weight: 500;
  color: #ed7227;
  line-height: 40px;
  padding-left: 10px;
}
.ticon::before {
  content: '|';
  right: 10px;
  background-color: #ed7227;
}
.bottomcss {
  margin-top: 20px;
}
.bgcss {
  padding: 20px 0px !important;
}
.listdiv {
  margin-top: 20px;
}
.headcss {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: right;
}
.addbtns {
  background-color: #ed7227;
  color: white;
  border: none !important;
}
.addbtns:hover {
  color: #ed7227;
  background-color: #ffefe5;
  border: none !important;
}
.addbtns:focus {
  color: #ed7227;
  background-color: #ffefe5;
  border: none !important;
}
</style>