<template>
  <div class=" mdivcss">
    <el-carousel motion-blur height="430px">
        <el-carousel-item
        v-for="(item, index) in banners"
        :key="index"
        >
        <img class="cursor" :src="item.imagsUrl" />
        </el-carousel-item>
    </el-carousel>
    <div class="w1280">
      <!-- <bread-crumb /> -->
      <div class="topItem">
        <img :src="apiData.coverUrl" alt="" class="leftImg">
        <div class="rightDetail">
          <div class="topTitle">
            <div class="title">{{ apiData.name }}</div>
          </div>
          <div class="bottomDl">
            <div class="leftDetail">
              <div class="text">专业大类：{{ apiData.specialName }}</div>
              <div class="text">层次：{{ levelMap[apiData.level] }}</div>
              <div class="text">主办单位：{{ apiData.organizer }}</div>
              <div class="joinBtn" 
                  @click="apiData.isOwner == 0 ? handleJoin() : ''" 
                  :class="{ 'disabled-btn': apiData.isOwner != 0 }">
                {{ apiData.isOwner == 0 ? '申请加入' : '已加入' }}
              </div>
            </div>
            <div class="content">
              <div class="intro" v-html="apiData.intro"></div>
              <el-button link class="moreBtn" @click="showIntroDialog">查看更多<el-icon><ArrowRight /></el-icon></el-button>
            </div>
          </div>

        </div>
 
      </div>


      <div class="box">
        <div class="leftBox">
            <div class="boxTitle"><span class="jianjie primary-text-color">教研室成员</span></div>
            <el-button link  @click="showMemberDialog" class="gengduo">查看全部<el-icon><ArrowRight /></el-icon></el-button>
            <div class="peopleItem">
              <div class="people" v-for="(item, index) in detailInfo.members" :key="index" @click="gotoUser(item)">
                <img :src="item.logo" alt="" class="peopleImg">
                <div class="peopleName">{{ item.name }}</div>
              </div>
            </div>
            <div v-if="detailInfo.members.length == 0" class="center">暂无成员</div>
        </div>
        <div class="rightBox">
            <div class="topBox">
                <div class="boxTitle"><span class="jianjie primary-text-color">常用工具</span></div>
                <div class="gjBox">
                    <div class="gjItem" v-for="(item,index) in gjList" :key="index" :style="{ background: item.bgColor }" @click="handleToolClick(item)">
                        <img :src=item.img alt="" class="gjImg">
                        <div class="gjTitle">{{ item.title }}</div>
                    </div>
                </div>
            </div>
            <div class="bottomBox">
                <div class="boxTitle"><span class="jianjie primary-text-color">基本数据</span></div>
                <div class="dataBox">
                    <div class="dataItem" v-for="(item,index) in dataList" :key="index" :style="{ background: item.bgColor }">
                        <img :src=item.img alt="" class="dataImg">
                        <div class="numberBox">
                            <div class="number">{{ item.number || 0 }}</div>
                            <div class="numberTitle">{{ item.title }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>


  


      <div class="resourceItem">
        <div class="topText">资源展示</div>
        <div class="typeList">
          <div class="active-indicator" :style="{ transform: `translateX(${activeTypeIndex * 116}px)` }"></div>
          <div 
            class="listItem" 
            v-for="(item, index) in ResourceTypes" 
            :key="index"
            :class="{ active: activeTypeIndex === index }"
            @click="changeActiveType(index, item.id)">
            {{item.name}}
          </div>
        </div>

        <div class="resource">
          <!-- 数字教材特殊UI -->
          <div v-if="selectedResourceType === 1" class="digitalTextbookList">
            <div
              class="digitalTextbookCard"
              v-for="(item, index) in resourceList"
              :key="'digital-' + index"
              @click="viewResource(item)">
              <img :src="item.coverUrl" alt="" class="digitalTextbookImg">
              <div class="bookName text-ellipsis-1">{{ item.name }}</div>
              <div class="bookTime">
                <div class="user">
                  <img src="@/assets/user.png" alt="" class="userImg">
                  <div class="chief">周远安</div>
                </div>
                <div class="time">
                  <img src="@/assets/time.png" alt="" class="userImg">
                  <!-- <span>{{ item.createTime }}</span> -->
                  <span>2025-07-23</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 其他资源类型的普通UI -->
          <template v-else>
            <div class="resourceContent">
              <div
                class="resourceList"
                v-for="(item, index) in resourceList"
                :key="'resource-' + index"
                @click="viewResource(item)">
                <img :src="item.coverUrl" alt="" class="resourceImg">
                <div class="mar8">
                  <div class="resourceTitle">{{ item.name }}</div>
                  <div class="zhujiang">
                    <div class="user">
                      <img src="@/assets/user.png" alt="" class="userImg">
                      <div class="chief">周远安</div>
                    </div>
                    <div class="resourceText">{{ item.createTime }}</div>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <div v-if="resourceList.length == 0" class="no-data">暂无资源</div>
          <div class="chakan">
            <div class="see" @click="goList">
              <div class="seeBtn">查看全部</div>
              <img src="@/assets/rightBtn.png" alt="" class="rightBtn">
            </div>
          </div>
        </div>
      </div>

      <div class="statisticsItem">
        <div class="topText">教研活动</div>
        <div class="mt45" v-for="(activity, index) in activityList" :key="index">
          <img :src="activity.coverUrl" alt="" class="activityImg">
          <div class="tag">线上活动</div>
          <div class="jiaoDetail">
            <div class="title">{{ activity.title }}</div>
            <div class="text activityContent">{{ activity.intro }}</div>
            <div class="text">活动时间：{{ activity.startTime }} 至 {{ activity.endTime }}</div>
            <div class="text">主讲人：{{ activity.name }}</div>
          </div>
          <el-button type="primary" class="joinBtn">立即报名</el-button>
        </div>
        <div v-if="activityList.length === 0" class="no-data">暂无活动</div>
      </div>

    </div>
    <!-- 教研室简介弹框 -->
    <el-dialog
      v-model="introDialogVisible"
      title="教研室简介"
      width="1100px"
      class="introDialog">
      <div class="intro-dialog-content" v-html="apiData.intro"></div>
    </el-dialog>
    <!-- 教研室成员弹框 -->
    <el-dialog
      v-model="memberDialogVisible"
      title="教研室成员"
      width="1100px"
      class="memberDialog">
      <div class="search-form">
        <el-form :model="searchParams" label-width="10px" >
          <div class="biaodan">
            <el-form-item label="">
                <el-input v-model="searchParams.name" placeholder="请输入姓名" />
              </el-form-item>
              <el-form-item label="">
                <el-input v-model="searchParams.unit" placeholder="请输入单位" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
              </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="member-content">
        <div class="memberBox">
          <div class="memberItem" v-for="(item, index) in detailInfo.members" :key="index" @click="gotoUser(item)">
            <img :src="item.logo" alt="" class="memberImg">
            <div class="userFlex">
              <div class="user">
                <div class="memberName">{{ item.name }}</div>
                <div class="zhuli">主理人</div>
              </div>
              <div class="unit text-ellipsis-1">北京航空航天科</div>
            </div>
          </div>
        </div>
      <div v-if="detailInfo.members.length == 0" class="center">暂无成员</div>
      </div>
      <el-pagination
            v-model:current-page="searchParams.pageNum"
            v-model:page-size="searchParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :background="true"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            style="margin-top: 20px; justify-content: center;"
        />
    </el-dialog>

    <!-- 申请加入弹框 -->
    <el-dialog
      v-model="joinDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="申请加入"
      width="500px"
      class="joinDialog"
      @close="handleDialogClose">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" class="join-form">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="年龄" prop="age">
          <el-input v-model="form.age" placeholder="请输入年龄" type="number" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="form.gender" placeholder="请选择性别" style="width: 100%">
            <el-option label="男" value="男" />
            <el-option label="女" value="女" />
          </el-select>
        </el-form-item>
        <el-form-item label="单位类型" >
          <el-radio-group v-model="form.unitType">
            <el-radio value="1">高校</el-radio>
            <el-radio value="2">企业</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model="form.unit" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="部门" prop="department">
          <el-input v-model="form.department" placeholder="请输入部门" />
        </el-form-item>
        <el-form-item label="职称" prop="title">
          <el-input v-model="form.title" placeholder="请输入职称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">提交申请</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {ArrowRight, Search} from '@element-plus/icons-vue'
import { jiaoyanDetail, applyJoinResearchOffice, getResearchOfficeUsers, getResourceList, getActivityList } from '@/api/api'
// import BreadCrumb from '@/components/BreadCrumb.vue'
import { ResourceTypes, ResourceTypesMap } from '@/utils/static-tools'
import tu1 from '@/assets/tu1.png'
import people from '@/assets/people.png'
const router = useRouter()
const route = useRoute()
const showFullIntro = ref(false)
const detailInfo = ref({
  title: '',
  content:'',
  teacher:'',
  time:'',
  members: [],
  statistics: [],
  activityImg: tu1,
  typeList:[{type:'数字教材'},
  {type:'微课' },
  {type:'教学课件'},
  {type:'教学设计'},
  {type:'教学视频'},
  {type:'会议录像'},
]
})
const banners = ref([
  {
    imagsUrl: 'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/digital-platform/null/1774375973526426407.png'
  },
  {
    imagsUrl: 'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/digital-platform/null/1774375973526426402.png'
  },
  {
    imagsUrl: 'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/digital-platform/null/1699125208742868475.png'
  },
])
const apiData = ref({})
const dialogVisible = ref(false)
const introDialogVisible = ref(false)
const memberDialogVisible = ref(false)
const joinDialogVisible = ref(false)
const searchParams = ref({
  pageNum: 1,
  pageSize: 10,
  name: '',
  unit: ''
})
const total = ref(0)

const form = ref({
  isOwner: 0,
  name: '',
  age: '',
  gender: '',
  unitType: '1',
  unit: '',
  department: '',
  title: '',
  researchofficeId: route.query.id,
  userId: localStorage.getItem('id')
})
const rules = ref({
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  unit: [{ required: true, message: '请输入单位', trigger: 'blur' }]
})
const activeTypeIndex = ref(0)
const levelMap = ref({
  1: '本科',
  2: '高职'
})

import icon14 from '@/assets/taolun.png'
import icon15 from '@/assets/data1.png'
import icon16 from '@/assets/data2.png'
import icon17 from '@/assets/data3.png'
import icon18 from '@/assets/data4.png'
import icon19 from '@/assets/data5.png'
import icon20 from '@/assets/data6.png'
import icon21 from '@/assets/data7.png'
import icon22 from '@/assets/data8.png'
import icon23 from '@/assets/data9.png'
import icon24 from '@/assets/data10.png'
import icon25 from '@/assets/data11.png'
import icon26 from '@/assets/data12.png'
import icon27 from '@/assets/data13.png'
import icon28 from '@/assets/data14.png'
import icon29 from '@/assets/data15.png'
const gjList = ref([
    {
        img: icon15,
        title:'工作台',
        bgColor: '#FEF8F6',
        type: '1'
    },
    {
        img: icon16,
        title:'资源库',
        bgColor: '#FFF9EA',
        type: '2'
    },
    {
        img: icon14,
        title:'讨论',
        bgColor: '#EDFCEF',
        type: '3'
    },
    {
        img: icon17,
        title:'会议',
        bgColor: '#F3F6FF',
        type: '4'
    },

]);
const dataList = ref([
    {
        img: icon18,
        number:'126',
        title:'工作台',
        bgColor: '#FEF5FF'
    },
    {
        img: icon19,
        number:'78',
        title:'线上会议',
        bgColor: '#F2F8FF'
    },
    {
        img: icon20,
        number:'52',
        title:'知识点',
        bgColor: '#F3F2FF'
    },
    {
        img: icon21,
        number:'12',
        title:'教学资源',
        bgColor: '#FCF9F2'
    },
    {
        img: icon22,
        number:'56',
        title:'参与教师',
        bgColor: '#F2F8FF'
    },
    {
        img: icon23,
        number:'12',
        title:'线下会议',
        bgColor: '#FCF9F2'
    },
    {
        img: icon24,
        number:'65',
        title:'知识图谱',
        bgColor: '#F3F2FF'
    },
    {
        img: icon25,
        number:'9',
        title:'教研成果',
        bgColor: '#FEF5FF'
    },
    {
        img: icon26,
        number:'20',
        title:'参与企业人员',
        bgColor: '#FEF8F6'
    },
    {
        img: icon27,
        number:'0',
        title:'线上直播',
        bgColor: '#FEF5FF'
    },
    {
        img: icon28,
        number:'9',
        title:'能力图谱',
        bgColor: '#FCF9F2'
    },
    {
        img: icon29,
        number:'65',
        title:'教研资料',
        bgColor: '#EDFCEF'
    },
]);
const resourceList = ref([])
const selectedResourceType = ref(1) // 初始化为数字教材的ID，与activeTypeIndex保持一致
const activityList = ref([])
// 方法定义
const getDetailInfo = () => {
  const id = route.query.id
  if (!id) return
  jiaoyanDetail({ id })
    .then(res => {
      if (res.data) {
        apiData.value = res.data
      }
    })
}
const handleJoin = () => {
  const token = localStorage.getItem('token')
  
  if (!token) {
    ElMessage.warning('请先登录')
    return
  }
  
  joinDialogVisible.value = true
}

const formRef = ref()

const handleDialogClose = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const submitForm = () => {
  if (!formRef.value) return

  formRef.value.validate((valid) => {
    if (valid) {
      applyJoinResearchOffice(form.value)
        .then(() => {
          ElMessage.success('申请成功，请等待主理人同意')
          joinDialogVisible.value = false
        })
    } else {
      ElMessage.warning('请填写完整的表单信息')
    }
  })
}

const changeActiveType = (index, typeId) => {
  activeTypeIndex.value = index
  selectedResourceType.value = typeId
  loadResources()
}


const showIntroDialog = () => {
  introDialogVisible.value = true
}

const showMemberDialog = () => {
  memberDialogVisible.value = true
}

const handleSearch = () => {
  console.log('搜索参数:', searchParams.value)
}
const getMembers = () => {
  const researchOfficeId = route.query.id
  if (!researchOfficeId) return

  getResearchOfficeUsers({ researchOfficeId })
    .then(res => {
      if (res.data && res.data.length) {
        detailInfo.value.members = res.data.map(item => ({
          logo: item.logo || people,
          name: item.userName,
          userId: item.userId
        }))
      }
    })
}



const gotoUser = (item) => {
  const url = router.resolve({
    path: '/user',
    query: {
      id: item.userId
    }
  }).href
  window.open(url, '_blank')
}
const loadResources = () => {
  const id = route.query.id
  if (!id) return

  const params = {
    relateId: id,
    resourceType: selectedResourceType.value,
    pageNum: 1,
    pageSize: 12
  }

  getResourceList(params).then(res => {
    resourceList.value = res.data
    total.value = res.page.total
  })
}

const viewResource = (item) => {
  let url = item.jumpUrl || item.resourceUrl
  if (url) {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url
    }
    window.open(url, '_blank')
  } else {
    ElMessage.warning('暂无可访问的链接')
  }
}

const loadActivities = () => {
  const id = route.query.id
  if (!id) return

  const params = {
    relateId: id,
    type: 3,
    pageNum: 1,
    pageSize: 10
  }

  getActivityList(params).then(res => {
    activityList.value = res.data
  })
}

const goList = () => {
  const id = route.query.id
  if (selectedResourceType.value == 1) {
    // 数字教材跳转到bookList
    router.push({
      path: '/jiaoyanshi/bookList',
      query: {
        id: id,
        type: selectedResourceType.value
      }
    })
  } else {
    // 其他资源跳转到resourceList
    router.push({
      path: '/jiaoyanshi/resourceList',
      query: {
        id: id,
        type: selectedResourceType.value
      }
    })
  }
}

const handleToolClick = (item) => {
  switch (item.type) {
    case '1':
      // 跳转到工作台
      break
    case '2':
      // 跳转到资源库
      router.push('/jiaoyanshi/resources')
      break
    case '3':
      router.push('/jiaoyanshi/discuss')
      break
    case '4':
      // 跳转到会议
      break
    default:
      console.warn('未知的工具类型:', item.type)
  }
}

const handleSizeChange = (val) => {
  searchParams.value.pageSize = val
  searchParams.value.pageNum = 1
}

const handleCurrentChange = (val) => {
  searchParams.value.pageNum = val
};


// 生命周期
onMounted(() => {
  getDetailInfo()
  getMembers()
  loadResources()
  loadActivities()
});




</script>

<style lang="scss" scoped>
.mdivcss{
  background-color: #F5F7FA;
  padding-bottom: 20px;
}
.topItem{
  width: 100%;
  height: 278px;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  margin-top: 20px;
  padding: 20px;
  display: flex;
}
.leftImg{
  width: 422px;
  height: 238px;
  margin-right: 30px;
  border-radius: 8px;
}
.jiaoDetail{
  margin-right: 60px;
}
.activityContent {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  height: auto !important;
}
.title{
  height: 30px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #2D2F33;
  line-height: 30px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 16px;
  width: 778px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.text {
  height: 24px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #878D99;
  margin-bottom: 20px;
}

.joinBtn{
  display: flex;
  justify-content: center;
  align-items: center;
  width: 112px;
  height: 36px;
  background: #386CFC;
  border-radius: 99px 99px 99px 99px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-top: 44px;
  cursor: pointer;
}
.peopleItem{
  display: grid;
  gap: 46px;
  grid-template-columns: repeat(5, 1fr);
}
.people{
  display: flex;
  align-items: center;
  flex-direction: column;
  cursor: pointer;
}
.peopleImg{
  width: 48px;
  height: 48px;
  border-radius: 50%;
  box-shadow: 0px 2px 16px 0px rgba(22,33,54,0.06);
}
.peopleName{
  height: 21px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #4B4D4B;
  line-height: 21px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-top: 5px;
}
.Introduction{
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  padding: 20px;
  margin-top: 20px;
  margin-bottom: 20px;
  position: relative;
  display: flex;
  flex-direction: column;
  
}
.topText{
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 136px;
  height: 45px;
  background: rgba(56,108,252,0.12);
  border-radius: 8px 0px 100px 0px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #386CFC;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.IntroductionContent{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #4B4D4B;
  line-height: 32px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-top: 45px;
  margin-bottom: 12px;
  
  &.collapsed {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.moreBtn,.el-icon-arrow-down{
  height: 21px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 14px;
  color: #386CFC;
  line-height: 21px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-left: auto;
}
.statisticsItem{
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  margin-bottom: 20px;
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
}
.shujuImg{
  width: 40px;
  height: 40px;
  margin-right: 16px;
}
.dataText{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: #2D2F33;
  font-style: normal;
  text-transform: none;
  margin-right: 30px;
}
.dataNumber{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: bold;
  font-size: 24px;
  color: #2D2F33;
  font-style: normal;
  text-transform: none;
}
.mt45{
  margin-top: 45px;
  display: flex;
  align-items: center;
  position: relative;
}
.activityImg{
  width: 295px;
  height: 166px;
  margin-right: 30px;
  border-radius: 8px;
}
.sumitBtn{
  width: 112px;
  height: 36px;
  background: #386CFC;
  border-radius: 99px 99px 99px 99px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  text-align: left;
  font-style: normal;
  text-transform: none;
  cursor: pointer;
}
.typeList{
  width: 702px;
  margin-top: 45px;
  background-color: #FCF8F4;
  display: flex;
  align-items: center;
  line-height: 48px;
  border-radius: 99px 99px 99px 99px;
  padding: 4px;
  position: relative;
}
.listItem{
  min-width: 116px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #4B4D4B;
  text-align: center;
  font-style: normal;
  text-transform: none;
  z-index: 1;
  cursor: pointer;
}
.active-indicator {
  position: absolute;
  width: 112px;
  height: 40px;
  background: #FC8F1A;
  border-radius: 99px 99px 99px 99px;
  transition: transform 0.3s ease;
  left: 4px;
}
.active {
  color: #FFFFFF;
  font-weight: 500;
}
.resourceItem{
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  margin-bottom: 20px;
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  margin-top: 20px;
}
.resourceList{
  width: 290px;
  border-radius: 8px;
}
.resourceImg{
  width: 295px;
  height: 166px;
  border-radius: 8px;
}
.zhujiang{
  display: flex;
  justify-content: space-between;
}
.resourceTitle{
  height: 24px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #2D2F33;
  line-height: 24px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-top: 12px;
  margin-bottom: 8px;
  width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.mar8{
  margin: 0 8px;
}
.resourceContent{
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(4, 1fr);
  align-items: center;
  margin-top: 30px;
  margin-bottom: 40px;
}
.dialog-footer{
  display: flex;
  align-items: center;
  justify-content: center;
}
.submitBtn{
  width: 280px;
  height: 42px;
  background: #386CFC;
  border-radius: 4px 4px 4px 4px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  font-style: normal;
  text-transform: none;
}

.tag{
  width: 76px;
  height: 27px;
  background: #FC8F1A;
  border-radius: 4px 4px 4px 4px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 13px;
  color: #FFFFFF;
  font-style: normal;
  text-transform: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 8px;
  top: 16px;
}

.disabled-btn {
  background: #cccccc !important;
  cursor: not-allowed !important;
}

.no-data {
  width: 100%;
  text-align: center;
  padding: 40px 0;
  color: #909399;
  font-size: 14px;
}

.resourceText {
  height: 21px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #909399;
  line-height: 21px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.resourceList {
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  width: 100%;
}

.digitalTextbookList {
  display: grid;
  gap: 30px;
  margin-top: 28px;
  grid-template-columns: repeat(5, 1fr);
}

.digitalTextbookCard {
  width: 192px;
  height: 318px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  box-sizing: border-box;
  cursor: pointer;
  margin-bottom: 20px;
}
.bookTime{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.time{
  display: flex;
  align-items: center;
}

.user{
  display: flex;
  align-items: center;
}
.chief{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #878D99;
}

.digitalTextbookImg {
  width: 100%;
  height: 254px;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #E1E4EB;
}

.bookName{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #44474D;
  margin-top: 12px;
  margin-bottom: 8px;
  width: 200px;
}
.bookTime{
  height: 21px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #909399;
  line-height: 21px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.box{
  display: flex;
  margin-top: 20px;
}
.leftBox{
  min-width: 493px;
  height: 598px;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  position: relative;
  padding: 30px;
  padding-top: 65px;
  margin-right: 20px;
  overflow-y: auto;
}
.boxTitle{
  min-width: 136px;
  height: 45px;
  background: #F5F8FF;
  border-radius: 8px 0px 100px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 0;
  top: 0;
}
.jianjie{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
}
.rightBox{
  max-width: 767px;
}
.topBox{
  position: relative;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  margin-bottom: 20px;
}
.bottomBox{
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
}
.gjBox{
  padding: 30px;
  padding-top: 61px;
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(4, 1fr);
}
.gjItem{
  width: 161px;
  height: 116px;
  padding-top: 10px;
  border-radius: 8px 8px 8px 8px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  flex-direction: column;
  cursor: pointer;
}
.gjTitle{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #2D2F33;
}
.dataBox{
  padding: 30px;
  padding-top: 61px;
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(4, 1fr);
}
.dataItem{
  width: 161px;
  height: 80px;
  border-radius: 8px 8px 8px 8px;
  display: flex;
  align-items: center;
  padding: 16px 12px;
}
.dataImg{
  width: 40px;
  height: 40px;
  margin-right: 12px;
}
.number{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: bold;
  font-size: 20px;
  color: #2D2F33;
  margin-bottom: 6px;
}
.numberTitle{
  min-width: 85px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #545A66;
}
.intro{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #44474D;
  line-height: 32px;
  max-height: 192px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 6;
  text-overflow: ellipsis;
}
.bottomDl{
  display: flex;
}
.leftDetail{
  min-width: 260px;
  margin-right: 40px;
}
.content{
  height: 192px;
}
.gjImg{
  width: 56px;
  height: 56px;
}
.moreBtn{
  width: 108px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 14px;
  color: #386CFC;
  position: absolute;
  right: 0;
  bottom: 5px;
  background: linear-gradient( 90deg, rgba(255,255,255,0.69) 0%, #FFFFFF 11%);
}

.intro-dialog-content {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #44474D;
  line-height: 32px;
  overflow-y: auto;
}
.userImg{
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.see{
  width: 130px;
  height: 36px;
  background: #386CFC;
  border-radius: 99px 99px 99px 99px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.rightBtn{
  width: 16px;
  height: 16px;
  margin-left: 2px;
}
.chakan{
  display: flex;
  justify-content: center;
}
.gengduo{
  position: absolute;
  right: 43px;
  top: 13px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 14px;
  color: #386CFC;
}
.biaodan{
  display: flex;
  align-items: center;
}
.memberBox{
  display: grid;
  gap: 20px;
  margin-top: 20px;
  grid-template-columns: repeat(5, 1fr);
}
.memberItem{
  display: flex;
  align-items: center;
  width: 192px;
  height: 72px;
  border-radius: 8px 8px 8px 8px;
  padding: 12px;
}
.memberItem:hover{
  background: #F5F7FA;
}
.memberImg{
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 8px;
}
.userFlex{
  display: flex;
  flex-direction: column;
}
.user{
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.zhuli{
  width: 57px;
  height: 21px;
  border-radius: 99px;
  border: 1px solid #FC8F1A;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 11px;
  color: #FC8F1A;
  margin-left: 8px;
}
.memberName{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 14px;
  color: #44474D;
}
.unit{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #878D99;
}
.member-content {
  min-height: 300px;
}
.center{
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
  font-size: 14px;
}
</style>
<style lang="scss">
.introDialog{
  padding: 0 !important;
  .el-dialog__header {
    height: 71px;
    padding: 20px 30px;
    background: linear-gradient( 180deg, #E7EDFE 0%, rgba(231,237,254,0) 100%) !important;
  }
  .el-dialog__body {
      padding: 30px !important;
      padding-top: 8px !important;
  }
  .el-dialog__headerbtn{
      font-size: 20px;
  }
}
</style>