<template>
    <el-dialog width="60%" title="新增教研室" v-model="dialogVisible" @close="close" :close-on-click-modal="false">
        <el-form :rules="rules" ref="addform" :model="form" label-width="100px">
            <el-form-item label="教研室名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入教研室名称" show-word-limit maxlength="20" clearable></el-input>
            </el-form-item>
            
            <el-form-item label="教研室封面" prop="coverUrl">
                <el-upload
                    class="upload-demo"
                    :action="getUrl"
                    name="file"
                    :limit="1"
                    accept=".jpg, .png, .JPG, .jpeg, .JPEG, .PNG, .gif"
                    :on-preview="handlePreview"
                    :on-remove="handleRemove"
                    :file-list="fileList"
                    :headers="headerUrl"
                    :on-success="handleAvatarSuccess"
                    :data="imgDetails"
                    list-type="picture"
                >
                    <el-button size="small" type="primary">点击上传封面</el-button>
                    <template #tip>
                        <div class="el-upload__tip">只能上传jpg/png文件，且不超过2M</div>
                    </template>
                    
                </el-upload>
            </el-form-item>
                            
            <el-form-item label="主办单位" prop="organizer">
                <el-input v-model="form.organizer" placeholder="请输入主办单位" show-word-limit maxlength="20" clearable></el-input>
            </el-form-item>
            
            <el-form-item label="层次" prop="level">
                <el-select v-model="form.level" placeholder="请选择层次" clearable>
                    <el-option label="本科" :value="1"></el-option>
                    <el-option label="高职" :value="2"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="专业" prop="specialId">
                <el-select v-model="form.specialId" placeholder="请选择专业" clearable>
                    <el-option
                        v-for="item in specialData"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="教研室简介" prop="intro" >
                <el-input type="textarea" v-model="form.intro" :rows="4" placeholder="请输入教研室简介" show-word-limit maxlength="1000"></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="close">取 消</el-button>
                <el-button type="primary" @click="onSubmit">确定</el-button>
            </div>
        </template>
        
    </el-dialog>
</template>

<script setup>
import { 
    jiaoyanlist, 
    labelList, 
    saveTeachingOffice, 
    jiaoyanDetail, 
    updateTeachingOffice, 
    deleteTeachingOffice 
} from '@/api/expert/index'
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { onMounted, reactive } from 'vue';
const route = useRoute()
const getUrl = computed(()=>{
  return import.meta.env.VITE_GLOB_BASE_URL + '/aliyun/oss/uploadFiles'
})
const dialogVisible = ref(false)
const headerUrl = ref({
  Authorization: localStorage.getItem('token') 
})
var fileList = ref([])
const imgDetails = ref({ serviceName: 'web' })
const dialogTitle = ref('新增教研室')
const levelMap = ref({
    1: '本科',
    2: '高职'
})
const form = reactive({
    id: '',
    name: '',
    coverUrl: '',
    coverName: '',
    coverSize: '',
    level: '',
    organizer: '',
    specialId: '',
    intro: '',
    })
const rules = ref({
    name: [
        { required: true, message: '请输入教研室名称', trigger: 'blur' }
    ],
    coverUrl: [
        { required: true, message: '请上传教研室封面', trigger: 'blur' }
    ],
    level: [
        { required: true, message: '请选择层次', trigger: 'change' }
    ],
    organizer: [
        { required: true, message: '请输入主办单位', trigger: 'blur' }
    ],
    specialId: [
        { required: true, message: '请选择专业', trigger: 'change' }
    ],
    intro: [
        { required: true, message: '请输入教研室简介', trigger: 'blur' }
    ]
})
const specialData = ref()
const addform = ref()
const show = () =>{
    dialogVisible.value = true;
}
function resetForm() {
    fileList.value = [];
    for (let key in form) {
        form[key] = '';
    }
}
function close() {
    addform.value.resetFields();
    resetForm()
    dialogVisible.value = false
}
function onSubmit() {
    addform.value.validate((valid) => {
        if (valid) {
            const request = this.form.id ? 
                updateTeachingOffice(this.form) : 
                saveTeachingOffice(this.form);
            
            request.then(res => {
                if (res.status == 0) {
                    this.$message.success(this.form.id ? '更新成功' : '保存成功');
                    this.dialogVisible = false;
                    this.loadTableData();
                } else {
                    this.$message.error(res.msg);
                }
            })
        } else {
            return false;
        }
    });
}
function handleRemove(file, fileList) {
    this.fileList = fileList;
    this.form.coverUrl = '';
    this.form.coverName = '';
    this.form.coverSize = '';
}
function handlePreview(file) {
    
}
function handleAvatarSuccess(res, file, fileList) {
    this.fileList = fileList;
    if (res.status == 0) {
        this.form.coverUrl = res.data.url;
        this.form.coverName = res.data.fileName;
        this.form.coverSize = res.data.size;
    } else {
        this.$message.error('上传失败：' + res.msg);
    }
}
function initSpecialData() {
    labelList({ tagtypeCode: 'TYPEMANAGER' }).then(res => {
        specialData.value = res.data;
    })
}
onMounted(()=>{
    initSpecialData()
})
defineExpose({
    show
})
</script>

<style lang="scss" scoped>
.upload-demo {
  .el-upload {
    width: 100%;
  }
}

.dialog-footer {
    display: flex;
    justify-content: center;
}

</style>