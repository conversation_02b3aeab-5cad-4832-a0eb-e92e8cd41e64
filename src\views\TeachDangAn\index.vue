<template>
    <div class="mdivcss">
    <div class="wid1160">
      <div class="teacher">
        <div>
          <span>教师姓名</span>
          <el-input class="inpute" v-model="pageBean.teacherName"></el-input>
          <el-button class="search" type="primary" @click="onSubmit"
            >查询</el-button
          >
          <el-button class="geren" type="primary" @click="gotoone"
            >个人档案袋</el-button
          >
        </div>
        <div class="divli">
          <div
            class="li"
            v-for="(item, index) in teacherList"
            @click="gotoDetail(item)"
          >
            <img
              class="img"
              v-if="item.imagesUrl"
              :src="item.imagesUrl"
              alt=""
            />
            <img v-else class="img" src="../../assets/zhao.png" alt="" />

            <p class="tname">{{ item.name }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { teacherList } from '@/api/index.js'
import { ElMessage } from 'element-plus'
import { nextTick, reactive, useTemplateRef } from 'vue'
import { useRoute,useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const curItem = ref(null)
const teaList = ref([])
const pageBean = reactive({
    teacherName: '',
    isCreate: 1,
})
function gotoone() {
    let token = window.localStorage.getItem('token')
    if (token != null) {
    router.push({
        path: '/jxdandai/detail',
        query: {
            isOne: 1,
            id: localStorage.getItem('id'),
        },
    })
    } else {
        ElMessage({
            type:'error',
            message:'请先登录'
        })
    }
}
function gotoDetail(item) {
    let token = window.localStorage.getItem('token')
    if (token != null) {
    router.push({
        path: '/jxdandai/detail',
        query: {
            id: item.id,
            name: item.name,
        },
    })
    } else {
         ElMessage({
            type:'error',
            message:'请先登录'
        })
    }
}
function teacherListApi() {
    teacherList(pageBean).then((res) => {
        if (res.status == 0) {
        teaList.value = res.data
        }
    })
}
teacherListApi()
function onSubmit() {
    teacherListApi()
}
function clickItem(data) {
    curItem.value = data
    const refs = useTemplateRef(data.ref)
    nextTick(()=>{
        refs.loaddata()
    })
}
</script>

<style lang="scss" scoped>
.geren {
  float: right;
}
.tname {
  text-align: center;
  font-size: 20px;
  height: 20px;
  line-height: 20px;
  margin-top: 20px;
}
.img {
  width: 100%;
  height: 270px;
}
.divli {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
}
.li {
  width: 18%;
  height: 340px;
  border: 1px solid #f5f5f5;
  cursor: pointer;
  margin-right: 2.2%;
  margin-bottom: 20px;
}
.li:nth-child(5n) {
  margin-right: 0px;
}

.mdivcss {
  background-color: #f5f7fa;
  overflow: hidden;
}
.search {
  height: 36px;
  margin-left: 16px;
  position: relative;
  top: 1px;
  padding: 10px 20px;
}
.inpute {
  width: 200px;
  height: 36px;
  margin-left: 16px;
    ::v-deep(.el-input__inner) {
        width: 200px;
        height: 36px;
    }
}
.teacher {
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
  margin-top: 20px;
  min-height: 620px;
  margin-bottom: 20px;
}
.tabitem {
  height: 45px;
  color: #666666;
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  font-family: Source Han Sans CN;
  line-height: 40px;
  padding: 0 16px;
  cursor: pointer;
}
.seltext {
  color: #ed7227 !important;
}
.sline {
  width: 24px;
  height: 3px;
  margin: 0 auto;
}
.seline {
  background-color: #ed7227 !important;
}
.tabs {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.wid1160 {
  width: 1160px;
  margin: 0 auto;
}
</style>