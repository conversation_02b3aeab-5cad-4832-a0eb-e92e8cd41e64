<template>
    <div ref="playerContainer" class="plyr-container"></div>
  </template>
  
  <script setup>
  import { ref, onMounted, onUnmounted, defineProps, defineEmits, watch } from 'vue';
  import Plyr from 'plyr';
  import 'plyr/dist/plyr.css';
  
  const props = defineProps({
    options: {
      type: Object,
      default: () => ({})
    },
    source: {
      type: String,
      required: true
    }
  });
  
  const emit = defineEmits(['ready', 'play', 'pause', 'ended']);
  const playerContainer = ref(null);
  let player = null;
  
  onMounted(() => {
    // 默认选项
    const defaultOptions = {
      controls: [
        'play-large', 'play', 'progress', 'current-time', 'mute', 
        'volume', 'captions', 'settings', 'pip', 'airplay', 'fullscreen'
      ],
      i18n: {
        restart: '重新播放',
        play: '播放',
        pause: '暂停',
        seekLabel: '跳转',
        volumeLabel: '音量',
        mute: '静音',
        unmute: '取消静音',
        enableCaptions: '启用字幕',
        disableCaptions: '禁用字幕',
        enterFullscreen: '全屏',
        exitFullscreen: '退出全屏'
      }
    };
  
    // 创建视频元素
    const videoElement = document.createElement('video');
    videoElement.src = props.source;
    videoElement.controls = true;
    videoElement.crossOrigin = '';
    playerContainer.value.appendChild(videoElement);
  
    // 初始化播放器
    player = new Plyr(videoElement, { ...defaultOptions, ...props.options });
  
    // 绑定事件
    player.on('ready', () => emit('ready', player));
    player.on('play', () => emit('play', player));
    player.on('pause', () => emit('pause', player));
    player.on('ended', () => emit('ended', player));
  });
  
  // 监听源变化
  watch(() => props.source, (newSource) => {
    if (player && newSource) {
      player.source = {
        type: 'video',
        sources: [{ src: newSource }]
      };
    }
  });
  
  onUnmounted(() => {
    if (player) {
      player.destroy();
      player = null;
    }
  });
  </script>
  
<style scoped>
.plyr-container {
    width: 100%;
    height: 100%;
}
</style>