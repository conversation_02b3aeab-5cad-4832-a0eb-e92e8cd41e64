<template>
  <div class="breadcrumb-container">
    <div class="title">当前位置：</div>
    <el-breadcrumb separator=">">
      <el-breadcrumb-item 
        v-for="(item, index) in breadcrumbList" 
        :key="index"
        :class="{ 'current-page': index === breadcrumbList.length - 1 }"
      >
        <span 
          v-if="index === breadcrumbList.length - 1"
          class="current-text"
        >
          {{ item.title }}
        </span>
        <router-link 
          v-else
          :to="item.path"
          class="parent-link"
        >
          {{ item.title }}
        </router-link>
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const breadcrumbList = computed(() => {
  if (route.meta?.breadcrumb) {
    return route.meta.breadcrumb
  }

  return [
    { title: '首页', path: '/site/home' }
  ]
});
</script>

<style scoped>
.breadcrumb-container {
  padding: 16px 0;
  display: flex;
  align-items: center;
}

:deep(.el-breadcrumb) {
  font-family: Source Han Sans CN, Source <PERSON> Sans CN;
  font-weight: 400;
  font-size: 14px;
}

:deep(.el-breadcrumb__separator) {
  color: #878D99;
  margin: 0 8px;
}

.parent-link {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  background: linear-gradient(1.1205755698169716e-7deg, #115CFF 23%, #9A68FF 88%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none;
  cursor: pointer;
}

.parent-link:hover {
  opacity: 0.8;
}

.current-text {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #878D99;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #878D99;
}
</style>
