<template>
  <div class="bg">
    <div class="main">
      <img src="../assets/logowhite.png" alt="" />
      <div class="nvas">
        <div class="menudiv">
          <div class="menu-item" v-for="(item, index) in menuList" :key="index">
            <span @click="handleClick(item.path)">{{ item.name }}</span>
          </div>
        </div>
        <div class="copyright">◎ 2018-2022 梧桐花科技有限责任公司 版权所有</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      menuList: [
        { name: '首页', path: '/' },
        { name: '教研室', path: '/jiaoyanshi' },
        { name: '课程资源', path: '/coursecenter' },
        { name: '培训项目', path: '/teachproject' },
        { name: 'AI工具', path: '/teachtool' },
        { name: '教师档案袋', path: '/jxdandai' },
      ],
    }
  },
  methods: {
    handleClick(path) {
      this.$router.push(path)
    },
  },
}
</script>

<style lang="scss" scoped>
.bg {
  width: 100%;
  height: 93px;
  background: #1e1e1e;
  box-shadow: 0px -4px 20px 0px rgba(117, 151, 203, 0.12);
  margin-top: auto;
}
.main {
  width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}
.menudiv {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.menu-item {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  margin-right: 80px;
  cursor: pointer;
}
.copyright {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 16px;
}
</style>