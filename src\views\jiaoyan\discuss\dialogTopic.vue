<template>
  <div>
    <el-dialog
      title="发布话题"
      v-model="dialogVisible"
      width="900px"
      @close="handleClose"
      class="mydialog"
      :close-on-click-modal="false"
    >
      <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item label="标题" prop="title" label-width="60px">
          <el-input v-model="form.title" placeholder="请输入话题标题" clearable maxlength="80" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="内容" label-width="60px">
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
            v-model="form.content"
            maxlength="2000"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="图片" label-width="60px">
          <EUploadImgVideo
            v-model="imgList"
            :limit="5"
            :onlyImage="true"
            accept="image/png,image/jpg,image/gif,image/jpeg"
            :acceptData="imgageData"
          >
            <div>
              <p class="wen">温馨提示：</p>
              <p>（1）支持jpg/jpeg/png格式； </p>
              <p>（2）建议单张图片不超过10M，最多可上传5张；</p>
            </div>
          </EUploadImgVideo>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <div class="tip">请注意您的发言，礼貌留言哦~</div>
          <el-button class="btns" type="primary" @click="submitForm">立即发布</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import EUploadImgVideo from '@/components/EUploadImgVideo.vue'
import { questionSave, updateTopic } from '@/api/study.js'
import { getFileTypeResult } from '@/utils/tools.js'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'

const route = useRoute()

const dialogVisible = ref(false)
const imgList = ref([])
const formRef = ref()

const imgageData = ref({
  accept: '.png,.jpg,.gif,.jpeg',
  maxsize: 1024 * 1024 * 10,
  maxsize_text: '10M',
  imageTypeList: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif']
})

const form = ref({
  id: '',
  title: '',
  content: '',
  courseId: route.query.courseId,
  type: 2,
  userType: localStorage.getItem('userType') == '6' ? 1 : 2,
  isRecover: 2,
  fileInfoEntityList: [],
})

const rules = ref({
  title: [
    { required: true, message: '请输入主题标题', trigger: 'blur' }
  ],
})

const emit = defineEmits(['refresh'])
const handleClose = () => {
  formRef.value.resetFields()
  form.value.content = ''
  form.value.title = ''
  imgList.value = []
}

const openDialog = (detailData) => {
  if (detailData) {
    form.value = {
      ...detailData,
      isRecover: 2,
    }
    imgList.value = detailData.fileInfoEntityList ? detailData.fileInfoEntityList.map(item => ({
      name: item.fileName,
      url: item.url,
      size: item.fileSize
    })) : []
  } else {
    form.value.id = ''
    form.value.title = ''
    form.value.content = ''
    imgList.value = []
  }
  dialogVisible.value = true
}

const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      form.value.fileInfoEntityList = imgList.value.map(item => ({
        url: item.url,
        fileName: item.name,
        fileSize: item.size,
        fileType: getFileTypeResult(item.url)
      }))
      const apiCall = form.value.id ? updateTopic : questionSave
      apiCall(form.value).then(() => {
        ElMessage.success(form.value.id ? '更新成功' : '发布成功')
        dialogVisible.value = false
        emit('refresh')
        handleClose()
      })
    } else {
      return false
    }
  })
}

defineExpose({
  openDialog
});
</script>

<style lang="scss" scoped>
.dialog-footer{
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.tip{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #8A8C89;
  margin-right: 32px;
}
.btns{
  width: 96px;
  height: 40px;
  background: #386CFC;
  border-radius: 4px 4px 4px 4px;
}
</style>
<style lang="scss">
.mydialog{
  padding: 0 !important;
  .el-dialog__header {
    height: 71px;
    padding: 20px 30px;
    border-bottom: 1px solid #E1E4EB;;
  }
  .el-dialog__body {
      padding: 60px !important;
      padding-top: 30px !important;
  }
  .el-dialog__footer{
    padding: 0 30px 30px !important;
  }
  .el-dialog__headerbtn{
      font-size: 22px;
      margin-right: 20px;
      margin-top: 20px;
  }

}
</style>

