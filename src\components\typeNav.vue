<template>
    <div class="box">
        <div class="nav-container">
            <div class="nav-row" v-for="(category, index) in categories" :key="index">
                <div class="nav-label">{{ category.label }}:</div>
                <div class="nav-items">
                    <div 
                        v-for="(item, itemIndex) in category.items" 
                        :key="itemIndex"
                        :class="['nav-item', { active: item.active }]"
                        @click="handleItemClick(index, itemIndex)"
                    >
                        <span class="navName" :class="['navName', { active: item.active }]">{{ item.name }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';

const categories = ref([
    {
        label: '层次',
        items: [
            { name: '全部', active: true },
            { name: '本科', active: false },
            { name: '职业本科', active: false },
            { name: '高职高专', active: false },
            { name: '中职中专', active: false },
            { name: '社会学习', active: false }
        ]
    },
    {
        label: '维度',
        items: [
            { name: '全部', active: true },
            { name: '通用技能', active: false },
            { name: '管理技能', active: false },
            { name: '专业技能', active: false }
        ]
    },
    {
        label: '类别',
        items: [
            { name: '全部', active: true },
            { name: 'JAVA技术', active: false },
            { name: 'BIM管理', active: false },
            { name: '需求分析与产品设计', active: false },
            { name: '前端开发', active: false },
            { name: '电子信息工程', active: false },
            { name: '电子商务', active: false },
            { name: '航空服务工程', active: false },
            { name: '旅游', active: false },
        ]
    },
    {
        label: '方式',
        items: [
            { name: '全部', active: true },
            { name: '线上学习', active: false },
            { name: '线下学习', active: false },
            { name: '混合式学习', active: false }
        ]
    }
]);

const handleItemClick = (categoryIndex, itemIndex) => {
    categories.value[categoryIndex].items.forEach(item => {
        item.active = false;
    });
    
    categories.value[categoryIndex].items[itemIndex].active = true;
    
};
</script>

<style lang="scss" scoped>
.box{
    width: 100%;
    height: auto;
    min-height: 192px;
    background: #FFFFFF;
    border-radius: 12px 12px 12px 12px;
    padding: 20px 30px;
}

.nav-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.nav-row {
    display: flex;
    align-items: center;
}

.nav-label {
    min-width: 50px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #5C5E66;
}

.nav-items {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;

}

.nav-item {
    min-width: 56px;
    height: 29px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    border-radius: 50px;
    padding: 0 12px;
    &.active {
        background: #F4F3FE;
        border-radius: 50px 50px 50px 50px;
        color: #2A2D33;
    }
}
.navName{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #2A2D33;
    &.active {
        background: linear-gradient(180.00000000000006deg, #8163EE 0%, #1872FF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
}
</style>
