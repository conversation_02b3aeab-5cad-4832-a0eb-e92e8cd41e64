<template>
    <div>
        <div class="">
        <el-carousel :interval="5000" arrow="hover" id="banner" height="430px">
            <el-carousel-item
            @click.native="bannerLinkDetails(item)"
            v-for="(item, index) in banners"
            :key="index"
            >
            <img class="cursor" :src="item.imagsUrl" />
            </el-carousel-item>
        </el-carousel>
        </div>
    </div>
    </template>
<!-- <script>
import { bannerList } from '@/api/index.js'
  export default {
    data() {
      return {
        banners: [],
      }
    },
    created() {
      this.getBanner()
    },
    methods: {
      async getBanner() {
        let res = await bannerList()
        this.banners = res.data
      },
      bannerLinkDetails(row) {
        if (row.skipUrlPortal) {
          window.open(row.skipUrlPortal)
        }
      },
    },
  }
</script> -->
<script setup>
import { bannerList } from '@/api/api'
import { ref } from 'vue'
const banners = ref([])
const getBanner = () => {
    bannerList().then(res => {
        banners.value = res.data
    })
}
const bannerLinkDetails = (row) => {
    if (row.skipUrlPortal) {
        window.open(row.skipUrlPortal)
    }
}
getBanner()

</script>
<style lang="scss" scoped>
    .cursor {
    cursor: pointer;
    }
    .el-carousel__container {
    width: 100%;
    }
    .el-carousel__container img {
    width: 100%;
    height: 100%;
    object-fit: cover; 
    object-position: center; 
    }
    #banner {
    overflow-y: hidden;
    }
</style>

  