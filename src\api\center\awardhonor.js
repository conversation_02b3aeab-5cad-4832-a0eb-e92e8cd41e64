import service from '@/utils/request.js'

export function getAwardinfo(id) {
  return service.request({
    method: 'get',
    url: `/ear/portal/honorsaward/info/${id}`,
  });
}
export function saveAward(data) {
  return service.request({
    method: 'post',
    url: `/ear/portal/honorsaward/save`,
    data
  });
}

export function updateAward(data) {
  return service.request({
    method: 'post',
    url: `/ear/portal/honorsaward/update`,
    data
  });
}


export function getListAllAward(data) {
  return service.request({
    method: 'get',
    url: `/ear/portal/honorsaward/list`,
    params: data
  });
}

export function deleteAward(data) {
  return service.request({
    method: 'post',
    url: `/ear/portal/honorsaward/delete`,
    data
  });
}
