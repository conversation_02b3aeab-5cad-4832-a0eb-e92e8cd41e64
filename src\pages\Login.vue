<template>
    <div class="login">
        <div class="box">
            <div class="leftBg">
                <div class="title">欢迎使用云端职业大学</div>
            </div>
            <div class="right">
                <img src="@/assets/topimg.png" alt="" class="topImg">
                <img src="@/assets/botimg.png" alt="" class="botImg">
                <div class="desc">欢迎使用~</div>
                <div class="bigTitle">请登录~</div>
                <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form">
                    <div class="biaoti">账号</div>
                    <el-form-item prop="username">
                        <el-input v-model="loginForm.username" type="text" placeholder="请输入账号">
                            <template #prefix>
                                <img src="@/assets/svg/account.svg" alt="用户图标" class="input-icon">
                            </template>
                        </el-input>
                    </el-form-item>
                    <div class="biaoti">密码</div>
                    <el-form-item prop="password">
                        <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" @keyup.enter="handleLogin">
                            <template #prefix>
                                <img src="@/assets/svg/password.svg" alt="密码图标" class="input-icon">
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item style="width:100%;">
                        <div :loading="loading" @click.prevent="handleLogin">
                            <span class="loginBtn">登 录</span>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
</template>
  
<script setup>
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import CryptoJS from 'crypto-js'
import { login } from '@/api/api.js'

const router = useRouter()

const loginFormRef = ref(null)
const loginForm = ref({
    username: '',
    password: ''
})

const loginRules = {
    username: [
        { required: true, message: '请输入账号', trigger: 'blur' }
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' }
    ]
}

const loading = ref(false)

function handleLogin() {
    if (!loginFormRef.value) return

    loginFormRef.value.validate((valid) => {
        if (valid) {
            loading.value = true

            // 对密码进行两次MD5加密
            const firstMd5 = CryptoJS.MD5(loginForm.value.password).toString()
            const secondMd5 = CryptoJS.MD5(firstMd5).toString()

            const loginData = {
                username: loginForm.value.username,
                password: secondMd5,
                userType: 3
            }

            login(loginData).then((res) => {
                ElMessage.success('登录成功')

                if (res.data && res.data.token) {
                    localStorage.setItem('token', res.data.token)

                    const userData = {
                        username: loginForm.value.username,
                        userType: 3,
                        ...res.data
                    }
                    localStorage.setItem('userInfo', JSON.stringify(userData))
                }

                router.push('/site/home')

            
            }).catch(() => {
                loading.value = false
            })
        }
    })
}
</script>
  
<style rel="stylesheet/scss" lang="scss" scoped>
.login {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-image: url("@/assets/login-background.png");
    background-size: cover;
}

.title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #707070;
}

.login-form {
    border-radius: 6px;
    background: #ffffff;
    width: 320px;
    .el-input {
        height: 38px;

        input {
            height: 38px;
        }
    }

    .input-icon {
        height: 16px;
        width: 16px;
        margin-right: 8px;
    }
}

.login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
}

.login-code {
    width: 33%;
    height: 38px;
    float: right;

    img {
        cursor: pointer;
        vertical-align: middle;
    }
}

.el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
}

.login-code-img {
    height: 38px;
}
.box{
    display:flex;
    height: 780px;
    box-shadow: 0px 8px 40px 0px rgba(28,86,180,0.2);
}
.leftBg{
    width: 800px;
    background-image: url("@/assets/leftlogin.png");
    background-size: cover;
    .title{
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 38px;
        color: #333333;
        margin-top: 123px;
    }
}
.right{
    height: 100%;
    width: 500px;
    background-color: #fff;
    border-radius: 10px;
    box-sizing: border-box;
    display:flex;
    flex-direction: column;
    position: relative;
    padding-left: 80px;
    padding-top: 182px;
}
.loginBtn{
    width: 320px;
    height: 48px;
    background: linear-gradient( 270deg, #8163EE 0%, #1872FF 100%);
    border-radius: 8px 8px 8px 8px;
    display:flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #FFFFFF;
    margin-top: 30px;
}
.topImg{
    position: absolute;
    right: 20px;
    top: 20px;
    width: 253px;
    height: 118px;
}
.botImg{
    position: absolute;
    right: 0;
    bottom: 0;
    width: 148px;
    height: 130px;
}
.desc{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    margin-bottom: 12px;
}
.bigTitle{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 32px;
    color: #333333;
    margin-bottom: 60px;
}
.biaoti{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 20px;
    color: #333333;
    margin-bottom: 15px;
}
:deep(){
    .el-form-item{
        margin-bottom: 30px;
    }
}
</style>