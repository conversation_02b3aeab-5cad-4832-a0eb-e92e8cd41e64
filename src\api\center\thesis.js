import service from '@/utils/request.js'
//列表
export function thesisList(data) {
    return service.request({
      method: 'get',
      url: '/ear/portal/papersreports/list',
      params: data
    });
}
//保存
export function thesisSave(data) {
    return service.request({
        method: 'post',
        url: '/ear/portal/papersreports/save',
        data: data
    });
}
//更新
export function thesisEdit(data) {
    return service.request({
        method: 'post',
        url: '/ear/portal/papersreports/update',
        data: data
    });
}
//详情
export function thesisInfo(id) {
    return service.request({
        method: 'get',
        url: `/ear/portal/papersreports/info/${id}`,
    });
}
//删除
export function thesisDelete(data) {
    return service.request({
        method: 'post',
        url: '/ear/portal/papersreports/delete',
        data: data
    });
}
