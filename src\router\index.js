import { createRouter, createWebHashHistory } from 'vue-router'
const router = createRouter({
    history: createWebHashHistory(),
    routes: [
        {
            path: '/',
            name: 'index',
            redirect: '/home',
            component: () => import('../pages/Index.vue'),
            children: [
                {
                    path: '/home',
                    name: 'home',
                    component: () => import('../views/HomeView.vue'),
                },
                {
                    path: '/center',
                    component: () => import('../views/center/center.vue'),
                    redirect: '/center/self',
                    children: [
                        {
                            path: '/center/self',
                            component: () => import('../views/center/self.vue')
                        },
                        {
                            path: '/center/jiaoyan',
                            component: () => import('../views/center/jiaoyan.vue')
                        },
                        {
                            path: '/center/material',
                            component: () => import('../views/center/material.vue')
                        },
                    ]
                },
                {
                    path: '/center/jysdetail',
                    component: () => import('../views/center/jysdetail/index.vue'),
                    redirect:'/center/jysdetail/material',
                    children:[
                            {
                            path: '/center/jysdetail/material',
                            component: () => import('../views/center/jysdetail/material.vue')
                        },
                        {
                            path: '/center/jysdetail/basicdata',
                            component: () => import('../views/center/jysdetail/basicdata.vue')
                        },
                        {
                            path: '/center/jysdetail/restype',
                            component: () => import('../views/center/jysdetail/restype.vue')
                        },
                        {
                            path: '/center/jysdetail/banner',
                            component: () => import('../views/center/jysdetail/banner.vue')
                        },
                    ]
                },
                {
                    path: '/newdetail',
                    component: () => import('../views/newdetail.vue')
                },
                {
                    path: '/coursecenter',
                    name: 'coursecenter',
                    component: () => import('@/views/CourseCenter/index.vue'),
                },
                {
                    path: '/coursecenter/detail',
                    name: 'coursedetail',
                    component: () => import('@/views/CourseCenter/detail.vue'),
                },
                {
                    path: '/jiaoyanshi',
                    name: 'jiaoyanshi',
                    component: () => import('../views/jiaoyan/index.vue'),
                },
                {
                    path: '/jiaoyanshi/detail',
                    name: 'jiaoyanshiDetail',
                    component: () => import('../views/jiaoyan/detail.vue'),
                },
                {
                    path: '/jiaoyanshi/bookList',
                    name: 'bookList',
                    component: () => import('../views/jiaoyan/bookList.vue'),
                },
                {
                    path: '/jiaoyanshi/resourceList',
                    name: 'resourceList',
                    component: () => import('../views/jiaoyan/resourceList.vue'),
                },
                {
                    path: '/jiaoyanshi/resources',
                    name: 'resources',
                    component: () => import('../views/jiaoyan/resources/index.vue'),
                },
                {
                    path: '/jiaoyanshi/resources/add',
                    name: 'resourcesAdd',
                    component: () => import('../views/jiaoyan/resources/add.vue'),
                },
                {
                    path: '/jiaoyanshi/discuss',
                    name: 'discuss',
                    component: () => import('../views/jiaoyan/discuss/index.vue'),
                },
                {
                    path: '/jiaoyanshi/discussDetail',
                    name: 'discussDetail',
                    component: () => import('../views/jiaoyan/discuss/detail.vue'),
                },
                {
                    path: '/jiaoyanshi/release',
                    name: 'release',
                    component: () => import('../views/jiaoyan/discuss/release.vue'),
                },
                {
                    path: '/jiaoyanshi/myReply',
                    name: 'myReply',
                    component: () => import('../views/jiaoyan/discuss/myReply.vue'),
                },
                {
                    path: '/jiaoyanshi/replyMe',
                    name: 'replyMe',
                    component: () => import('../views/jiaoyan/discuss/replyMe.vue'),
                },

                {
                    path: '/teachproject',
                    name: 'teachproject',
                    component: () => import('@/views/TeachProject/index.vue'),
                },
                {
                    path: '/teachproject/detail',
                    name: 'teachprojectdetail',
                    component: () => import('@/views/TeachProject/detail.vue'),
                },
                {
                    path: '/teachproject/onlinedetail',
                    name: 'teachprojectonlinedetail',
                    component: () => import('@/views/TeachProject/onlinedetail.vue'),
                },
                {
                    path: '/teachtool',
                    name: 'teachtool',
                    component: () => import('@/views/TeachTool/index.vue'),
                },
                {
                    path: '/jxdandai',
                    name: 'jxdandai',
                    component: () => import('@/views/TeachDangAn/index.vue')
                },
                {
                    path: '/jxdandai/detail',
                    name: 'jxdandai/detail',
                    component: () => import('@/views/TeachDangAn/detail.vue')
                },


                //教研室讨论
                {
                    path: '/msgboard/index',
                    name: 'msgboard',
                    component: () => import('@/components/msgboard/index.vue'),
                },
                {
                    path: '/user',
                    name: 'user',
                    component: () => import('@/views/user/index.vue'),
                },
            ]
        },
        {
            path: '/login',
            name: 'login',
            component: () => import('../pages/Login.vue')
        },
        {
            path: '/admin/:url',
            name: 'admin',
            component: () => import('../pages/Admin.vue')
        },
    ]
})

export default router
