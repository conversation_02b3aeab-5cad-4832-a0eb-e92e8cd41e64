<template>
  <div class="online-detail">
    <back>返回</back>
    <div class="content">
      <div class="course-section">
        <h2 class="section-title">{{courseInfo.title}}</h2>
        <div class="course-list">
          <div class="course-item" v-for="(item, index) in courseInfo.topics" :key="index">
            <span class="item-index">专题{{item.index}}</span>
            <div class="item-content">
              <span class="item-title">{{item.title}}</span>
              <span class="item-desc">{{item.desc}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import back from '@/components/back.vue';
import { reactive } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute()
var courseInfo = reactive({
    title:'',
    topics:[]
})
const getCourseDetail = async () =>{
    const id = route.query.id;
      if (id == '1674604338506477703') {
        courseInfo.title = '《AIGC 商业应用实战教程》'
        courseInfo.topics = [
            { index: '一', title: '商海沧舟，智绘未来', desc: 'AIGC 概念初探' },
            { index: '二', title: '创作利器，智能助手', desc: 'AIGC 工具全览' },
            { index: '三', title: '文思泉涌，妙笔生花', desc: 'AIGC 辅助商业文本生成' },
            { index: '四', title: '智能画布，商演新篇', desc: 'AIGC 辅助商业演示设计' },
            { index: '五', title: '数据洞察，智慧决策', desc: 'AIGC 数据洞察，智慧决策' },
            { index: '六', title: '设计革新，推广增效', desc: 'AIGC 辅助商业图片制作' },
            { index: '七', title: '言之有智，沟通无界', desc: 'AIGC 辅助商务沟通优化' },
            { index: '八', title: '市场绘卷，品牌飞扬', desc: 'AIGC 赋能自媒体运营' },
          ]
      } else if (id == '1674604338506477699') {
        courseInfo.title ='《AIGC 教师应用实战教程》' 
        courseInfo.topics = [
            { index: '一', title: '智慧启航', desc: 'AIGC 技术初探' },
            { index: '二', title: '学情洞察', desc: 'AIGC 赋能教学分析' },
            { index: '三', title: '教学资源', desc: 'AIGC 助力跨学科整合' },
            { index: '四', title: '课件生成', desc: 'AIGC 助力教学课件生成' },
            { index: '五', title: '微课制作', desc: 'AIGC 助力教学内容多媒体呈现' },
            { index: '六', title: '课堂互动', desc: 'AIGC 助力课堂管理优化' },
            { index: '七', title: '课后拓学', desc: 'AIGC 助力个性化智能辅导' },
            { index: '八', title: '考试测评', desc: 'AIGC 助力教学评估自动化' },
            { index: '九', title: '教师科研', desc: 'AIGC 助力科研能力提升与实践' },
            { index: '十', title: '明规守矩', desc: 'AIGC 教育应用的可持续发展与规范使用' },
          ]
      }
}
getCourseDetail()
</script>
<style lang="scss" scoped>
.online-detail {
    width: 1280px;
    height: 100vh;
    margin: 0 auto;
    padding: 20px;
  
  .content {
    margin-top: 20px;
    background: none !important;
  }

  .section-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
  }

  .course-list {
    .course-item {
      width: 500px;
      display: flex;
      align-items: center;
      padding: 15px;
      margin-bottom: 15px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .item-index {
        background: #f0f7ff;
        color: #1890ff;
        padding: 6px 12px;
        border-radius: 4px;
        font-weight: bold;
        min-width: 80px;
        text-align: center;
      }

      .item-content {
        margin-left: 15px;
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .item-title {
          font-size: 16px;
          color: #333;
          font-weight: 500;
        }

        .item-desc {
          color: #666;
          font-size: 14px;
        }
      }
    }
  }
}
</style>