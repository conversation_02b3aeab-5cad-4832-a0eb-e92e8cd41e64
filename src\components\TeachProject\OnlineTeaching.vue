<template>
  <div class="gridContainer">
    <div class="mainItem" v-for="(item, index) in roomList" :key="index" @click="goDetail(item)">
      <div class="mainItemTop">
        <img :src="item.coverUrl" alt="" class="topicImg">
      </div>
      <div class="mainItemMiddle">
        <div class="mainItemMiddleTop">
          <div class="mainItemMiddleTitle">
            {{ item.title }}
          </div>
        </div>
        <div class="time">
          <img src="../../assets/time.png" alt="" class="weizhiImg">
          <div class="timeText">{{ item.createTime }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { jiaoyanlist } from '@/api/home/<USER>'

const router = useRouter()

const roomList = ref([])
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  userId: '',
  schoolId: '',
  type: 2
})

const getList = () => {
  jiaoyanlist(queryParams.value)
    .then(res => {
      if (res.status == 0) {
        roomList.value = res.data
      }
    })
}

const goDetail = (item) => {
  router.push({
    path: '/teachproject/onlinedetail',
    query: {
      id: item.id
    }
  })
}

onMounted(() => {
  getList()
})

defineExpose({
  getList
})
</script>

<style lang="scss" scoped>
.topicImg{
  max-width: 305px;
  height: 172px;
  border-radius: 8px 8px 0px 0px;
}
.mainItem{
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
}
.mainItem:hover{
  cursor: pointer;
  box-shadow: 0px 2px 20px 0px rgba(56,108,252,0.16);
}
.gridContainer{
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  width: 100%;
}
.mainItemMiddle{
  display: flex;
  justify-content: space-between;
  padding: 12px;
  flex-direction: column;
}
.mainItemMiddleTop{
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}
.mainItemMiddleTitle{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #2D2F33;
  line-height: 24px;
}
.time{
  display: flex;
  align-items: flex-start;
}
.timeText{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #878D99;
  margin-left: 6px;
  line-height: 21px;
}
.mainItemTop {
  position: relative;
}
.weizhiImg {
  margin-top: 3px;
}
</style> 