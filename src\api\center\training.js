import service from '@/utils/request.js'
//列表
export function trainingList(data) {
    return service.request({
        method: 'get',
        url: '/ear/portal/trainingfurther/list',
        params: data
    });
}
//保存
export function trainingfurtherSave(data) {
    return service.request({
        method: 'post',
        url: '/ear/portal/trainingfurther/save',
        data: data
    });
}
//更新
export function trainingfurtherEdit(data) {
    return service.request({
        method: 'post',
        url: '/ear/portal/trainingfurther/update',
        data: data
    });
}
//详情
export function trainingfurtherInfo(id) {
    return service.request({
        method: 'get',
        url: `/ear/portal/trainingfurther/info/${id}`,
    });
}
//删除
export function trainingfurtherDelete(data) {
    return service.request({
        method: 'post',
        url: '/ear/portal/trainingfurther/delete',
        data: data
    });
}
