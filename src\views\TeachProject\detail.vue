<template>
  <div class="oudiv w1200px">
    <div class="breamdiv main">
        <back>返回</back>
    </div>
      <div class="main mt70 bgmain">
        <div class="contentdiv">
          <h4 class="zititle">{{ detailInfo.name }}</h4>
          <p class="timetext">{{ detailInfo.createTime }}</p>
          <div class="line"></div>
          <div class="cssfont" v-html="detailInfo.content">
           </div>
        </div>
      </div>
  </div>
</template>

<script setup>
  import {
    jiaoyanInfo,
  } from '@/api/expert.js'
  import back from '@/components/back.vue';
  import { useRoute } from 'vue-router';
  const route = useRoute()
  console.log('ssss',route.query)
  const detailInfo = ref({})
  const getDetail = () =>{
    jiaoyanInfo({ id:route.query.id }).then(res => {
        if (res.status == 0) {
        detailInfo.value = res.data
        }
    })
  }
  getDetail()
</script>

<style lang="scss" scoped>
.cssfont{
  overflow-wrap: break-word;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #4B4D4B;
  line-height: 32px;
  text-align: justify;
  font-style: normal;
  text-transform: none;
  display: -webkit-box;
  overflow-wrap: break-word;
  overflow: hidden;
  -webkit-box-orient: vertical;
}

.news{
  font-size: 14px;
  font-weight: 400;
  color: rgba(59, 131, 255, 1);
  cursor: pointer;
}
.breamdiv{
  height: 70px;
  line-height: 70px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
}
.oudiv{
  padding-bottom: 70px;
  margin: 0 auto;
  background: #F5F7FA;
}
.bgmain{
  background: #fff;
}
.timetext{
  text-align: center;
  margin-top: 40px;
  font-size: 12px;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
}
.line{
  height: 0px;
  border-bottom: 1px solid rgba(230, 230, 230, 1);         
  margin: 40px 0;   
}
.mt70{
  padding-top: 40px;
}
.contentdiv{
  width:1000px ;
  margin: 0 auto;
  min-height: 441px;
  background: #fff;
  padding-bottom: 40px;
}
.zititle{
  font-size: 24px;
  font-weight: 700;
  color: rgba(51, 51, 51, 1);
  line-height: 34px;
}
</style>