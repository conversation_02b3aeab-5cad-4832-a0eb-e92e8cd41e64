import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'

export const userStore = defineStore('user', () => {
  const loginAccount = ref('')
  const getPicUrl = ref('')
  const nickname = ref('')
  const userType = ref('')
  const schoolId = ref('')
  const changeLoginAccount = (value) => {
    loginAccount.value = value
  }
  const changePicUrl = (value) => {
    getPicUrl.value = value
  }
  const changeNickName = (value) => {
    nickname.value = value
  }
  const changeUserType = (value) => {
    userType.value = value
  }
  const changeSchoolId = (value) => {
    schoolId.value = value
  }
  const clearState = () => {
    loginAccount.value = ''
    getPicUrl.value = ''
    nickname.value = ''
    userType.value = ''
    schoolId.value = ''

  }
  return {
    loginAccount,
    changeLoginAccount,
    changePicUrl,
    getPicUrl,
    nickname,
    changeNickName,
    userType,
    changeUserType,
    schoolId,
    changeSchoolId,
    clearState
  }
}, {
  persist: {
    paths: ['userInfo'],
  },
})