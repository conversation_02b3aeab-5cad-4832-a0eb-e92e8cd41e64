<template>
  <div class="bgcss">
    <div class="headcss">
      <div class="ticon">其他</div>
      <el-button v-if="isShow" class="addbtns" size="small" @click="showAdd"
        >添加其他</el-button
      >
    </div>
    <div class="listdiv">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="name" label="名称"> </el-table-column>

        <el-table-column prop="reportTime" label="发布时间" width="180">
        </el-table-column>
        <el-table-column prop="edit" label="操作" width="180" v-if="isShow">
          <template #default="scope">
            <el-button size="small" type="text" @click="editAction(scope.row)"
              >编辑</el-button
            >
            <el-divider direction="vertical"></el-divider>
            <el-button
              size="small"
              type="text"
              class="delbtn"
              @click="deleteAction(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="bottomcss">
      <el-pagination
        :current-page="pageBean.pageNum"
        :page-size="pageBean.pageSize"
        :total="total"
        @current-change="changePage"
        background
      ></el-pagination>
    </div>
    <el-dialog
      class="mydialog"
      top="50px"
      v-model="dialogFormVisible"
      :title="dTitle"
      @close="close2"
    >
      <el-form label-width="100px" :model="form" :rules="rules" ref="myform">
        <el-form-item label="名称：" prop="name">
          <el-input placeholder="请输入名称" v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="发布时间：" prop="reportTime">
          <el-date-picker
            v-model="form.reportTime"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="close2">取 消</el-button>
        <el-button type="primary" @click="submitAction">保 存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  thesisList,
  thesisSave,
  thesisDelete,
  thesisInfo,
  thesisEdit,
} from '@/api/center/thesis'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()
const isShow = ref(false)
const dialogFormVisible = ref(false)
const isEdit = ref(false)
const editId = ref(null)
const dTitle = ref('')
const pageBean = reactive({
    pageNum: 1,
    pageSize: 4,
    type: 3,
})
const tableData = ref([])
const total = ref(0)
const form = reactive({
  name: '',
  type: 3,
  reportTime: '',
})
const rules = ref({
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    reportTime: [
      { required: true, message: '请选择申报时间', trigger: 'change' },
    ],
})
function loaddata() {
  thesisList(pageBean).then((result) => {
    tableData.value = result.data
    total.value = result.page.total
  })
}
function changePage(page) {
  pageBean.pageNum = page
  loaddata()
}
function showAdd() {
  editId.value = ''
  dialogFormVisible.value = true
  dTitle.value = '添加其他'
  let obj = form
  Object.keys(obj).forEach((key) => {
    if (key != 'type') {
      obj[key] = ''
    }
  })
}
function editAction(row) {
  dialogFormVisible.value = true
  dTitle.value = '编辑'
  isEdit.value = true
  editId.value = row.id
  getDetailInfo(row.id)
}
function getDetailInfo(id) {
  thesisInfo(id).then((result) => {
    if (result.data) {
      Object.assign(form,result.data)
    } else {
      ElMessage.error('获取详情失败')
    }
  })
}
const myform = ref()
function close2() {
  dialogFormVisible.value = false
  nextTick(() => {
    myform.value.resetFields()
  })
}
function deleteAction(row) {
  ElMessageBox.confirm('此操作将删除当前数据, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      thesisDelete({ id: row.id }).then((result) => {
        if (result.data) {
          ElMessage({
            type: 'success',
            message: '删除成功!',
          })
          loaddata()
        } else {
          ElMessage.error(result.msg)
        }
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除',
      })
    })
}
function submitAction() {
  myform.value.validate((valid) => {
    if (valid) {
      form.type = 3
      if (form.id) {
        delete form.createTime
      }
      const action = form.id ? thesisEdit : thesisSave
      action(form).then((result) => {
        if (result.data) {
          ElMessage.success(this.form.id ? '编辑成功' : '保存成功')
          loaddata()
          close2()
        } else {
          ElMessage.error(result.msg)
        }
      })
    } else {
      return false
    }
  })
}
onMounted(()=>{
  pageBean.createBy = route.query.id
  if (localStorage.getItem('id') == route.query.id) {
    isShow.value = true
  }
  loaddata()
})
</script>

<style lang="scss" scoped>
.delbtn {
  color: #f56c6c;
  cursor: pointer;
}
.ticon {
  font-size: 14px;
  font-weight: 500;
  color: #ed7227;
  line-height: 40px;
  padding-left: 10px;
}
.ticon::before {
  content: '|';
  right: 10px;
  background-color: #ed7227;
}
.bottomcss {
  margin-top: 20px;
}
.bgcss {
  padding: 20px 0px !important;
}
.listdiv {
  margin-top: 20px;
}
.headcss {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: right;
}
.addbtns {
  background-color: #ed7227;
  color: white;
  border: none !important;
}
.addbtns:hover {
  color: #ed7227;
  background-color: #ffefe5;
  border: none !important;
}
.addbtns:focus {
  color: #ed7227;
  background-color: #ffefe5;
  border: none !important;
}
</style>