import service from '@/utils/request.js'
export function majorList(data) {
  return service.request({
    method: 'get',
    url: '/goldcourse/web/specialtyclassify/list',
    params: data
  });
}
export function StudentList(data) {
  return service.request({
    method: 'get',
    url: '/goldcourse/web/studentcourserelation/list',
    params: data
  });
}
export function CoursewareList(data) {
  return service.request({
    method: 'get',
    url: '/goldcourse/web/gradecoursewareinfo/list',
    params: data
  });
}
export function updateSaveList(data) {
  return service.request({
    method: 'post',
    url: '/goldcourse/web/gradecoursewareinfo/update',
    data
  });
}
export function addMajor(data) {
  return service.request({
    method: 'post',
    url: `/goldcourse/web/specialtyclassify/save`,
    data
  });
}

export function addCourseware(data) {
  return service.request({
    method: 'post',
    url: `/goldcourse/web/gradecoursewareinfo/save`,
    data
  });
}


export function updateMajor(data) {
  return service.request({
    method: 'post',
    url: `/goldcourse/web/specialtyclassify/update`,
    data
  });
}


export function deleteMajor(data) {
  return service.request({
    method: 'post',
    url: `/goldcourse/web/specialtyclassify/delete`,
    data
  });
}


export function deleteCourseware(data) {
  return service.request({
    method: 'post',
    url: `/goldcourse/web/gradecoursewareinfo/delete`,
    data
  });
}

export function deleteStudent(data) {
  return service.request({
    method: 'post',
    url: `/goldcourse/web/studentcourserelation/delete`,
    data
  });
}


export function updateInfo(data) {
  return service.request({
    method: 'post',
    url: `/web/portaluserinfo/update`,
    data
  });
}

export function updatePassword(data) {
  return service.request({
    method: 'post',
    url: `/goldcourse/web/portaluserlogin/updatePassword`,
    data
  });
}
export function info() {
  return service.request({
    method: 'get',
    url: `/web/portaluserinfo/info`,
  });
}

export function materialList(data) {
  return service.request({
    method: 'get',
    url: `/goldcourse/web/onlinelearningresource/list`,
    params: data
  });
}

export function materialSave(data) {
  return service.request({
    method: 'get',
    url: `/goldcourse/web/onlinelearningresource/save`,
    data
  });
}


// /ear/portal/filebagcourse/save
// /ear/portal/filebagcourse/info/{id}
// /ear/portal/filebagcourse/delete
// 档案袋-开设课程
export function filebagcourseSave(data) {
  return service.request({
    method: 'post',
    url: `/ear/portal/filebagcourse/save`,
    data
  });
}
export function filebagcourseUpdate(data) {
  return service.request({
    method: 'post',
    url: `/ear/portal/filebagcourse/update`,
    data
  });
}
export function filebagcourseDelete(data) {
  return service.request({
    method: 'post',
    url: `/ear/portal/filebagcourse/delete`,
    data
  });
}
export function filebagcourseInfo(id) {
  return service.request({
    method: 'get',
    url: `/ear/portal/filebagcourse/info/${id}`,
  });
}
export function filebagcourseList(params) {
  return service.request({
    method: 'get',
    url: `/ear/portal/filebagcourse/list`,
    params
  });
}

// /ear/portal/teachingmaterial/list
// /ear/portal/teachingmaterial/info/{id}
// /ear/portal/teachingmaterial/save
// /ear/portal/teachingmaterial/update
// /ear/portal/teachingmaterial/delete
// 档案袋-教材
export function teachingmaterialList(params) {
  return service.request({
    method: 'get',
    url: `/ear/portal/teachingmaterial/list`,
    params
  });
}
export function teachingmaterialInfo(id) {
  return service.request({
    method: 'get',
    url: `/ear/portal/teachingmaterial/info/${id}`,
  });
}

export function teachingmaterialSave(data) {
  return service.request({
    method: 'post',
    url: `/ear/portal/teachingmaterial/save`,
    data
  });
}

export function teachingmaterialUpdate(data) {
  return service.request({
    method: 'post',
    url: `/ear/portal/teachingmaterial/update`,
    data
  });
}

export function teachingmaterialDelete(data) {
  return service.request({
    method: 'post',
    url: `/ear/portal/teachingmaterial/delete`,
    data
  });
}


// 教学改革项目


export function teachingprojectList(params) {
  return service.request({
    method: 'get',
    url: `/ear/portal/teachingproject/list`,
    params
  });
}
export function teachingprojectInfo(id) {
  return service.request({
    method: 'get',
    url: `/ear/portal/teachingproject/info/${id}`,
  });
}

export function teachingprojectSave(data) {
  return service.request({
    method: 'post',
    url: `/ear/portal/teachingproject/save`,
    data
  });
}

export function teachingprojectUpdate(data) {
  return service.request({
    method: 'post',
    url: `/ear/portal/teachingproject/update`,
    data
  });
}

export function teachingprojectDelete(data) {
  return service.request({
    method: 'post',
    url: `/ear/portal/teachingproject/delete`,
    data
  });
}