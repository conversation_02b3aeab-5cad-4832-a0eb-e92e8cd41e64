<template>
    <div class="mdivcss">
      <div class="wid bgcss">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="线上教研" name="online">
            <online-teaching ref="onlineTeaching" />
          </el-tab-pane>
          <el-tab-pane label="线下教研" name="offline">
            <offline-teaching ref="offlineTeaching" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </template>
  
  <script setup>
  import OnlineTeaching from '@/components/TeachProject/OnlineTeaching.vue'
  import OfflineTeaching from '@/components/TeachProject/OfflineTeaching.vue'

  const activeTab = ref('online')
  const onlineTeaching = ref(null)
  const offlineTeaching = ref(null)

  const handleTabClick = () => {
    if (activeTab.value === 'online') {
      onlineTeaching.value?.getList()
    } else {
      offlineTeaching.value?.getList()
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .wid{
    width: 1280px;
  }
  .bgcss{
    margin: 0px auto;
    margin-bottom: 151px;
  }
  .mdivcss{
    padding: 20px 0px;
    background-color: #F5F7FA;
    min-height: calc(100vh - 93px);
  }
  .filter-container {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px 30px;
  }
  .mainContent{
    margin-top: 20px;
  }
  .topicImg{
    max-width: 305px;
    height: 172px;
    border-radius: 8px 8px 0px 0px;
  }
  .mainItem{
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
  }
  .mainItem:hover{
    cursor: pointer;
    box-shadow: 0px 2px 20px 0px rgba(56,108,252,0.16);
  }
  .gridContainer{
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    width: 100%;
  }
  .mainItemMiddle{
    display: flex;
    justify-content: space-between;
    padding: 12px;
    flex-direction: column;
  }
  .mainItemMiddleTop{
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }
  .mainItemMiddleTitle{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #2D2F33;
    line-height: 24px;
  }
  .mainItemNumber{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
  }
  .mainItemBottom{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .mainItemBottomTitle{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
    line-height: 20px;
  }
  .number{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
  }
  .topNav{
    width: 218px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    display: flex;
    padding: 3px;
  }
  .topNavTitle{
    width: 100px;
    height: 38px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px 6px 6px 6px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-size: 20px;
    color: #2D2F33;
    cursor: pointer;
  
    &.active {
      background: rgba(56,108,252,0.12);
      color: #386CFC; 
      font-weight: 500;
    }
  }
  .mr22{
    margin-right: 22px;
  }
  .time{
    display: flex;
    align-items: flex-start;
  }
  .timeText{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
    margin-left: 6px;
    line-height: 21px;
  }
  .btn{
    width: 88px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background: #386CFC;
    border-radius: 99px 99px 99px 99px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 14px;
    color: #FFFFFF;
    margin-top: 12px;
    cursor: pointer;
  }
  .mt10{
    margin-top: 8px;
  }
  .mainItemTop {
    position: relative;
  }
  
  .videoTag {
    position: absolute;
    top: 0;
    left: 0;
    padding: 6px 12px;
    background: #FC8F1A;
    border-radius: 8px 0px 12px 0px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 13px;
    color: #FFFFFF;
  }
  
  .weizhiImg {
    margin-top: 3px;
  }
  
  </style>