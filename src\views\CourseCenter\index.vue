<template>
    <div class=" mdivcss">
    <div class="wid bgcss">
        <!-- <div class="filter-container">
          <FilterRow
            name="课程主题"
            :modelValue="selectedTheme"
            @update:modelValue="selectedTheme = $event"
            :list="themeList"
          />
          <FilterRow
            name="课程类型"
            :modelValue="selectedLevel"
            @update:modelValue="selectedLevel = $event"
            :list="levelList"
          />
        </div> -->
        <!-- <div class="mainContent">
          <div class="gridContainer" v-if="roomList.length">
            <div class="mainItem" v-for="(item, index) in roomList" :key="index" @click="playVideo(item)">
              <div class="mainItemTop">
                <img :src="item.coverUrl" alt="" class="topicImg">
              </div>
              <div class="mainItemMiddle">
                <div class="mainItemMiddleTop">
                  <div class="mainItemMiddleTitle">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
          <EmptyState v-else
            :emptyImage="require('@/assets/nodataSize.png')"
          />
        </div> -->
        <div class="leftContent">
          <div class="topTitle">课程分类</div>
          <div 
            v-for="(item, index) in levelList" 
            :key="index" 
            class="typeList"
            :class="{ 'active': selectedLevel === item.label }"
            @click="selectedLevel = item.label"
          >
            {{ item.label }}
          </div>
        </div>

        <div class="mainContent">
          <div class="gridContainer" v-if="roomList.length">
            <div class="mainItem" v-for="(item, index) in roomList" :key="index" @click="goDetail(item)">
              <div class="mainItemTop">
                <img :src="item.coverUrl" alt="" class="topicImg">
              </div>
              <div class="mainItemMiddle">
                <div class="mainItemMiddleTop">
                  <div class="mainItemMiddleTitle">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
          <EmptyState v-else
            :emptyImage="nodataSize"
          />
        </div>
    </div>
  </div>
</template>

<script setup>
import EmptyState from '@/components/common/EmptyState.vue'
import nodataSize from '@/assets/nodataSize.png';
import { listAll } from '@/api/common/index.js'
import { homeList } from '@/api/home/<USER>'
import { onMounted, reactive } from 'vue'
import { useRouter,useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
const selectedTheme = ref('全部')
const selectedLevel = ref('')
const selectedForm = ref('全部')
var themeList = ref([])
var levelList = ref([])
const roomList = ref([])
let queryParams = reactive({
        pageNum: 1,
        pageSize: 10,
        userId: '',
        schoolId: '',
        courseType: 1,
        courseClassifyId: '',
        coureResourceType: ''
})
const getThemeList = () => {
    const param = {
        tagtypeCode: 'RESOURCETOPIC' 
    }
    listAll(param)
    .then(res => {
        if (res.status == 0) {
        themeList.value = res.data.map(item => ({
            label: item.name,
            value: item.name,
            id: item.id
        }))
        themeList.value.unshift({
            label: '全部',
            value: '全部',
            id: ''
        })
        }
    })
    .catch(() => {})
}
const getLevelList = () => {
      const param = {
        tagtypeCode: 'CLASSTYPE' 
      }
      listAll(param)
        .then(res => {
          if (res.status == 0) {
            levelList.value = res.data.map(item => ({
              label: item.name,
              value: item.name,
              id: item.id
            }))
          }
        })
        .catch(() => {})
    }
const getList = () => {
      homeList(queryParams)
        .then(res => {
          if (res.status == 0) {
            roomList.value = res.data
          }
        })
    }
const goDetail = (item) => {
    router.push({
        path: '/coursecenter/detail',
        query: {
            id: item.id
        }
    })
}
onMounted(()=>{
    getThemeList()
    getLevelList()
    getList()
})
</script>

<style lang="scss" scoped>
.wid{
  width: 1280px;
}
.bgcss{
  margin: 0 auto;
  display: flex;
}
.mdivcss{
  padding: 20px 0px;
  background-color: #F5F7FA;
  min-height: calc(100vh - 93px);
}
.filter-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px 30px;
}
.mainContent{
  width: 100%;
  margin-top: 20px;
  background: #FFFFFF;
  margin-left: 20px;

}
.topicImg{
  width: 100%;
  height: 172px;
  border-radius: 8px 8px 0px 0px;
}
.mainItem{
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
}
.mainItem:hover{
  cursor: pointer;
  box-shadow: 0px 2px 20px 0px rgba(56,108,252,0.16);
}
.gridContainer{
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  width: 100%;
}
.mainItemMiddle{
  display: flex;
  justify-content: space-between;
  padding: 12px;
  padding-bottom: 16px;
  flex-direction: column;
}
.mainItemMiddleTop{
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}
.mainItemMiddleTitle{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #2D2F33;
}
.mainItemNumber{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #878D99;
}
.mainItemBottom{
  display: flex;
  justify-content: space-between;
}
.mainItemBottomTitle{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #878D99;
  line-height: 20px;
}
.number{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #878D99;
}

.video-dialog {
  .el-dialog__body {
    padding: 0;
  }
}

.video-player {
  width: 100%;
  height: 550px;
}
.leftContent{
  min-width: 220px;
  height: 100vh;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
}
.topTitle{
  height: 52px;
  background: #386CFC;
  border-radius: 8px 8px 0px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  font-style: normal;
  text-transform: none;
  margin-bottom: 44px;
}
.typeList{
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #2D2F33;
  line-height: 19px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-bottom: 24px;
}
.typeList:hover{
  cursor: pointer;
  color: #386CFC;
  background: rgba(56,108,252,0.12);
}
.active {
  background: rgba(56,108,252,0.12);
  color: #386CFC;
}
</style>