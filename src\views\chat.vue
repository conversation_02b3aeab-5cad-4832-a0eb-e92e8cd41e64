<template>
  <div class="headbra">
    <span>当前位置：</span>
    <span class="bcolor" @click="goMenu">教研室</span>
    <img src="../assets/arrow.png" alt="" />
    <span class="secondspan" @click="backHandle">用户信息</span>
    <img src="../assets/arrow.png" alt="" />
    <span class="lastspan">我的消息</span>
  </div>
  <div class="private-message-container">
    <div class="left-section">
      <div class="contact-list" ref="userContainer" @scroll="handleScroll2">
        <div
          v-for="contact in contacts"
          :key="contact.recipientId"
          class="contact-item"
          :class="{ active: selectedContactId === contact.recipientId }"
          @click="handleSelectContact(contact.recipientId)"
        >
          <div class="avatar">
            <img :src="contact.avatar" alt="avatar" />
          </div>
          <div class="contact-info">
            <div class="name">{{ contact.nickname }}</div>
          </div>
          <img class="closecss" src="../assets/siclose.png" alt="" />
        </div>
      </div>
    </div>

    <!-- 右侧聊天区域 -->
    <div class="right-section">
      <template v-if="selectedContactId">
        <div class="headbox">
          <img class="userimg" src="../assets/1.png" alt="avatar" />
          <div class="username">周宇楠</div>
        </div>
        <div
          class="chat-messages"
          ref="messageContainer"
          @scroll="handleScroll"
        >
          <template v-for="(message, date) in groupedMessages" :key="date">
            <div
              class="message-item"
              :class="{
                sent: message.senderId !== currentUserId,
                received: message.senderId === currentUserId,
              }"
            >
              <div class="avatar">
                <img :src="message.avatar" />
              </div>
              <div class="message-content">
                <div class="message-bubble">
                  <!-- 文本消息 -->
                  <template v-if="message.type === 1">
                    {{ message.content }}
                  </template>
                  <!-- 图片消息 -->
                  <template v-else-if="message.type === 2">
                    <img
                      :src="message.content"
                      class="message-image"
                      @click="handleImagePreview(message.content)"
                    />
                  </template>
                </div>
                <p class="time">2025-02-25</p>
              </div>
            </div>
          </template>
        </div>

        <!-- 聊天输入区域 -->
        <div class="chat-input-area">
          <div class="input-toolbar">
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              style="display: none"
              @change="handleImageSelect"
            />
            <img
              src="@/assets/fileinput.png"
              class="toolbar-icon"
              @click="handleImageClick"
            />
          </div>
          <div class="input-box">
            <textarea
              :maxlength="maxLength"
              rows="3"
              v-model="messageContent"
              placeholder="请输入消息内容"
              @keyup.enter.ctrl="handleSendMessage"
            ></textarea>
          </div>
          <div class="sendbox">
            <span class="char-count"
              >{{ remainingChars }} / {{ maxLength }}</span
            >
            <div class="send-button">
              <button @click="handleSendMessage">发送</button>
            </div>
          </div>
        </div>
      </template>

      <template v-else>
        <div class="no-chat-selected">
          <img src="@/assets/images/wxzlxr.png" class="no-contact-selected" />
        </div>
      </template>
    </div>
  </div>

  <!-- 添加图片预览模态框 -->
  <div v-if="previewVisible" class="image-preview-modal" @click="closePreview">
    <div class="preview-content">
      <img :src="previewImage" class="preview-image" />
    </div>
  </div>

  <!-- 修改发起私聊弹窗 -->
  <div v-if="showStartChat" class="start-chat-modal" @click.self="handleCancel">
    <div class="modal-content">
      <div class="modal-header">
        <div class="search-box">
          <div class="input-wrapper">
            <input
              v-model="searchKeyword"
              type="text"
              placeholder="搜索联系人"
            />
            <span
              v-if="searchKeyword"
              class="clear-btn"
              @click.stop="searchKeyword = ''"
              >×</span
            >
          </div>
        </div>
      </div>
      <div class="contacts-container">
        <div
          v-for="contact in allContacts"
          :key="contact.id"
          class="contact-item"
          :class="{ selected: selectedNewContact === contact.id }"
          @click="handleSelectNewContact(contact.id)"
        >
          <div class="radio-box">
            <div
              class="radio-inner"
              :class="{ checked: selectedNewContact === contact.id }"
            ></div>
          </div>
          <img :src="contact.avatar" class="avatar" />
          <span class="name">{{ contact.nickname }}</span>
        </div>
      </div>
      <div class="modal-footer">
        <button class="cancel-btn" @click="handleCancel">取消</button>
        <button
          class="confirm-btn"
          :disabled="!selectedNewContact"
          @click="handleConfirmSelection"
        >
          确认
        </button>
      </div>
    </div>
  </div>
</template>
 
<script lang="ts" setup>
import { onMounted, ref, computed, nextTick, onUnmounted, watch } from 'vue'
// import { xxx, xxx, xxx, xxx } from '@/api/xxx/index'
// import { getAccessToken, getTenantId } from '@/utils/auth'
const selectedContactId = ref(true)
const selectedTab = ref<string>('1')
// 输入框内容
const messageContent = ref('')
// 模拟联系人数据 - 使用时间戳
const contacts = ref([
  {
    avatar:
      'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/digital-app/web/userhead/1727377452076353907.png',
    nickname: '周宇楠',
  },
])

// 模拟消息数据
const messages = ref([
  {
    senderId: 0,
    type: 1,
    createTime: '2025-04-24',
    content:
      '今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好',
    avatar:
      'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/digital-app/web/userhead/1727377452076353907.png',
  },
  {
    senderId: 0,
    type: 1,
    createTime: '2025-04-24',
    content:
      '今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好',
    avatar:
      'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/digital-app/web/userhead/1727377452076353907.png',
  },
  {
    senderId: 0,
    type: 1,
    createTime: '2025-04-24',
    content:
      '今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好',
    avatar:
      'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/digital-app/web/userhead/1727377452076353907.png',
  },
  {
    senderId: 0,
    type: 1,
    createTime: '2025-04-24',
    content:
      '今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好今天天气很好',
    avatar:
      'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/digital-app/web/userhead/1727377452076353907.png',
  },
  {
    senderId: 1,
    type: 1,
    createTime: '2025-04-24',
    content: '今天天气很好',
    avatar:
      'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/digital-app/web/userhead/1727377452076353907.png',
  },
  {
    senderId: 1,
    type: 1,
    createTime: '2025-04-24',
    content: '今天天气很好',
    avatar:
      'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/digital-app/web/userhead/1727377452076353907.png',
  },
  {
    senderId: 1,
    type: 1,
    createTime: '2025-04-24',
    content: '今天天气很好',
    avatar:
      'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/digital-app/web/userhead/1727377452076353907.png',
  },
])

// 添加当前用户ID
const currentUserId = ref(0) // 这里需要替换为实际的当前用户ID

// 修改消息分组逻辑
const groupedMessages = computed(() => {
  // 1. 首先对所有消息按时间排序（从早到晚）
  const sortedMessages = [...messages.value].sort(
    (a, b) => a.createTime - b.createTime
  )
  return sortedMessages
})

console.log(groupedMessages.value)

// 添加分页参数
const queryParams2 = ref({
  pageNo: 1,
  pageSize: 10,
  memberId: null as number | null,
})

// 是否正在加载更多
const isLoading = ref(false)

// 记录上一次的滚动位置
const lastScrollTop = ref(0)

// 聊天框滚动到顶部
const handleScroll = async (e) => {
  const target = e.target as HTMLElement
  // 当滚动到顶部时（考虑一个小的阈值）
  if (target.scrollTop <= 10 && !isLoading.value) {
    console.log('dingbu')
    isLoading.value = true

    try {
      // 增加页码
      queryParams2.value.pageNo++

      // 调用加载更多数据的方法
      const res = await xxx(queryParams2.value)

      if (res.list?.length) {
        // 保存当前的滚动位置
        const oldHeight = target.scrollHeight

        // 将新消息添加到现有消息列表的前面
        messages.value = [...res.list, ...messages.value]

        // 等待 DOM 更新
        await nextTick()

        // 调整滚动位置，保持原有内容的位置不变
        target.scrollTop = target.scrollHeight - oldHeight
      } else {
        // 没有更多数据了
        queryParams2.value.pageNo--
        message.info('没有更多消息了')
      }
    } catch (error) {
      console.error('加载更多消息失败:', error)
      queryParams2.value.pageNo--
    } finally {
      isLoading.value = false
    }
  }
}
// 联系人列表滚动到底部
const handleScroll2 = async (e: Event) => {
  const target = e.target as HTMLElement
  const isBottom =
    target.scrollHeight - target.scrollTop <= target.clientHeight + 10

  // 判断是否是向下滚动
  const isScrollingDown = target.scrollTop > lastScrollTop.value

  // 更新上一次的滚动位置
  lastScrollTop.value = target.scrollTop

  // 当向下滚动到底部时
  if (isBottom && isScrollingDown && !isLoading.value) {
    isLoading.value = true

    try {
      // 增加页码
      queryParams.pageNo++

      // 调用加载更多数据的方法
      const res = await xxx(queryParams)

      if (res.list?.length) {
        // 将新联系人添加到现有列表
        contacts.value = [...contacts.value, ...res.list]
        conversationTotal.value = res.total
      } else {
        // 没有更多数据了
        queryParams.pageNo--
        message.info('没有更多联系人了')
      }
    } catch (error) {
      console.error('加载更多联系人失败:', error)
      queryParams.pageNo--
    } finally {
      isLoading.value = false
    }
  }
}

// 选择联系人获取聊天记录的方法
const userData = ref({})
const messageTotal = ref(0)
const handleSelectContact = (contactId: number) => {
  selectedContactId.value = contactId
  currentUserId.value = contactId
  // 重置分页参数
  queryParams2.value = {
    pageNo: 1,
    pageSize: 10,
    memberId: contactId,
  }

  // 查找并赋值用户数据
  const currentUser = contacts.value.find(
    (contact) => contact.recipientId === contactId
  )
  if (currentUser) {
    userData.value = currentUser
  }

  xxx(queryParams2.value).then((res) => {
    messages.value = res.list
    messageTotal.value = res.total
    setTimeout(() => {
      scrollToBottom()
    }, 500)
  })
}

// 创建隐藏的文件上传输入框
const fileInput = ref<HTMLInputElement | null>(null)

// 处理图片上传点击
const handleImageClick = () => {
  fileInput.value?.click()
}

// 添加消息容器的ref
const messageContainer = ref<HTMLElement | null>(null)
const userContainer = ref<HTMLElement | null>(null)

// 滚动到底部的函数
const scrollToBottom = () => {
  nextTick(() => {
    const container = messageContainer.value
    if (container) {
      container.scrollTop = container.scrollHeight
    }
  })
}

// 修改发送消息函数
const handleSendMessage = () => {
  if (!messageContent.value.trim()) return
  let params = {
    type: '1',
    content: messageContent.value,
    recipientId: queryParams2.value.memberId,
  }
  xxx(params).then((res) => {
    message.success('发送成功')
    handleSelectContact(currentUserId.value)
    messageContent.value = ''
    getUser()
  })
}

// 修改图片选择函数
const actionUrl = ref(`${import.meta.env.VITE_BASE_URL}/文件存储接口地址`)
const headers = ref({
  // Authorization: 'Bearer ' + getAccessToken(),
  // 'tenant-id': getTenantId(),
})
const handleImageSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      message.error('请选择图片文件')
      return
    }

    try {
      // 1. 创建 FormData 对象上传文件
      const formData = new FormData()
      formData.append('file', file)

      // 2. 上传文件到存储服务
      const response = await fetch(actionUrl.value, {
        method: 'POST',
        headers: headers.value,
        body: formData,
      })

      const result = await response.json()
      if (result.code === 0) {
        // 3. 发送图片消息，使用响应中的 path
        const params = {
          type: '2',
          content: result.data, // 使用返回的文件路径
          recipientId: queryParams2.value.memberId,
        }

        // const res = await xxx(params)
        if (res) {
          message.success('发送成功')
          messages.value.push({
            senderId: '',
            nickname: userData.value.nickname,
            content: result.data,
            time: Date.now(),
            type: 2,
          })
          messageContent.value = ''
          scrollToBottom()
        }
      } else {
        message.error('图片上传失败')
      }
    } catch (error) {
      message.error('图片处理失败')
    }
  }

  // 清空文件选择
  target.value = ''
}

// 获取已存在聊天的用户数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  readStatus: 0,
})
const conversationTotal = ref(0)
const getUser = async () => {
  if (selectedTab.value == '1') {
    queryParams.readStatus = 0
    queryParams.pageNo = 1
  } else if (selectedTab.value == '2') {
    queryParams.readStatus = 1
    queryParams.pageNo = 1
  } else if (selectedTab.value == '3') {
    queryParams.readStatus = 2
    queryParams.pageNo = 1
  }

  try {
    // const res = await xxx(queryParams)
    // contacts.value = res.list
    // conversationTotal.value = res.total
  } catch (error) {
    console.error('获取联系人列表失败:', error)
    message.error('获取联系人列表失败')
  }
}

/** 初始化 **/
onMounted(() => {
  scrollToBottom()
  getUser()
})

// 监听消息列表变化
watch(
  () => messages.value.length,
  () => {
    scrollToBottom()
  }
)

// 添加预览相关的状态
const previewVisible = ref(false)
const previewImage = ref('')

// 处理图片点击预览
const handleImagePreview = (imageUrl: string) => {
  previewImage.value = imageUrl
  previewVisible.value = true
}

// 关闭预览
const closePreview = () => {
  previewVisible.value = false
  previewImage.value = ''
}

// 发起私聊相关状态
const showStartChat = ref(false)
const searchKeyword = ref('')
const allContacts = ref([])
// 搜索用户
watch(
  () => searchKeyword.value,
  (newValue) => {
    allContacts.value = []
    if (newValue != '') {
      xxx({ searchValue: newValue }).then((res) => {
        allContacts.value = res
      })
    }
  }
)

// 选中的新联系人
const selectedNewContact = ref<string | null>(null)

// 处理选择新联系人
const handleSelectNewContact = (contactId: string) => {
  selectedNewContact.value = contactId
}

// 处理确认选择
const handleConfirmSelection = () => {
  if (!selectedNewContact.value) return

  const selectedContact = allContacts.value.find(
    (c) => c.id === selectedNewContact.value
  )
  if (!selectedContact) return

  // 检查是否已存在该联系人
  const existingContact = contacts.value.find(
    (c) => c.id === selectedContact.id
  )
  if (!existingContact) {
    // 添加新联系人到列表
    contacts.value.push({
      recipientId: selectedContact.id,
      nickname: selectedContact.nickname,
      lastMessage: '暂无消息',
      time: Date.now(),
      unreadCount: 0,
      avatar: selectedContact.avatar,
      projectCount: selectedContact.projectCount,
    })
    userData.value = {
      id: selectedContact.id,
      nickname: selectedContact.nickname,
      avatar: selectedContact.avatar,
      projectCount: selectedContact.projectCount,
    }
  }

  // 选择该联系人并关闭弹窗
  selectedContactId.value = selectedContact.id
  currentUserId.value = selectedContact.id
  handleSelectContact(selectedContact.id)
  showStartChat.value = false
  selectedNewContact.value = null
}

// 处理取消
const handleCancel = () => {
  showStartChat.value = false
  selectedNewContact.value = null
}

// 处理发起私聊按钮点击
const handleStartChat = () => {
  showStartChat.value = true
}

const maxLength = ref(500)

const remainingChars = computed(() => {
  return messageContent.value.length
})

const backHandle = () => {}
const goMenu = () => {}
</script>
 
<style lang="scss" scoped>
.username {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 18px;
  color: #000000;
  margin-left: 12px;
}
.userimg {
  width: 38px !important;
  height: 38px !important;
  border-radius: 50%;
}
.lastspan {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #878d99;
}
.secondspan {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #44474d;
  cursor: pointer;
}
.bcolor {
  color: #386cfc !important;
  cursor: pointer;
}
.headbra {
  display: flex;
  align-items: center;
  height: 61px;
  line-height: 61px;
  width: 1280px;
  margin: 0 auto;
  color: #44474d;
}
.char-count {
  margin-right: 12px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #878d99;
}
.sendbox {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.headbox {
  padding-left: 30px;
  display: flex;
  height: 62px;
  border-bottom: 1px solid #e1e4eb;
  background: #fff;
  align-items: center;
}
.closecss {
  display: none;
}
.private-message-container {
  border-radius: 12px;
  display: flex;
  height: 808px;
  width: 1280px;
  margin: 0 auto;
  margin-bottom: 60px;
}

.left-section {
  width: 220px;
  border-right: 1px solid #e1e4eb;
  background: #fff;
}

.header {
  display: flex;
  padding: 16px;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;

  .active {
    color: #ff6427;
  }
}

.start-chat-btn {
  width: 116px;
  height: 40px;
  cursor: pointer;
  border-radius: 5px;
}

.contact-list {
  height: calc(100% - 73px);
  overflow-y: auto;
}

.contact-item {
  height: 88px;
  display: flex;
  align-items: center;
  padding: 20px;
  cursor: pointer;
  .right-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
  }
}

.contact-item:hover {
  background: #f5f7fa;
  .closecss {
    display: block;
  }
}

.avatar img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

.contact-info {
  margin-left: 16px;
  flex: 1;
}

.name {
  margin-bottom: 4px;
  font-size: 14px;
}

.last-message {
  max-width: 200px;
  overflow: hidden;
  font-size: 12px;
  color: #999;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.right-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;
}

.chat-header {
  display: flex;
  padding: 10px 20px;
  align-items: center;
  justify-content: space-between;
  background-color: #ffefe9;
  border-bottom: 1px solid #eee;

  .chat-title {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;

    .avatar-img {
      width: 48px;
      height: 48px;
      border-radius: 50%;
    }
  }
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: white;

  .message-time-divider {
    margin: 16px 0;
    font-size: 12px;
    color: #999;
    text-align: center;
  }

  // 添加平滑滚动
  scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.message-item {
  display: flex;
  margin-bottom: 12px;

  &.received {
    .message-bubble {
      margin-left: 12px;
      background-color: #f5f7fa;
      border-radius: 0 8px 8px 8px;
      border-radius: 0px 12px 12px 12px;
      padding: 12px 16px;
    }
    .time {
      margin-left: 12px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 13px;
      color: #878d99;
      margin-top: 8px;
    }
  }

  &.sent {
    flex-direction: row-reverse;

    .message-bubble {
      margin-right: 12px;
      color: #44474d;
      background-color: #dfe7fb;
      border-radius: 12px 0 12px 12px;
    }
    .time {
      margin-right: 12px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 13px;
      color: #878d99;
      margin-top: 8px;
    }

    .message-content {
      align-items: flex-end;
    }
  }

  .avatar img {
    width: 38px;
    height: 38px;
    border-radius: 50%;
  }

  .message-content {
    display: flex;
    flex-direction: column;
    max-width: 50%;
  }

  .message-bubble {
    padding: 10px 16px;
    max-width: 200%;
    word-break: break-all;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474d;
    line-height: 24px;
    .message-image {
      max-width: 200px;
      max-height: 200px;
      border-radius: 4px;
    }
  }
}

.chat-input-area {
  display: block;
  background-color: white;
  padding: 16px 30px;
  border-top: 1px solid #e1e4eb;

  .input-toolbar {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;

    .toolbar-icon {
      width: 30px;
      height: 30px;
      cursor: pointer;
    }
  }

  .input-box {
    margin-bottom: 12px;
    height: 72px;
    textarea {
      padding: 0;
      line-height: 24px;
      width: 100%;
      border: none;
      resize: none;
      outline: none;
      font-size: 14px;
      color: #000;
      &::placeholder {
        color: #999;
      }
    }
  }

  .send-button {
    display: flex;
    justify-content: flex-end;
    button {
      padding: 10px 30px;
      background-color: #386cfc;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
  }
}

.no-chat-selected {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;

  .no-contact-selected {
    width: 280px;
    height: 348px;
  }
}

.contact-item.active {
  background-color: #fff7f5;
}

.contact-item.active .name,
.contact-item.active .last-message,
.contact-item.active .message-time {
  color: #ff6427;
}

.contact-item:hover {
  background-color: #f5f5f5;
}

.contact-item.active:hover {
  background-color: #fff7f5;
}

.emoji-container {
  position: relative;
}

.emoji-panel {
  position: absolute;
  bottom: 100%;
  left: 0;
  width: 380px;
  padding: 12px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  margin-bottom: 8px;

  .emoji-item {
    font-size: 24px;
    cursor: pointer;
    text-align: center;
    padding: 4px;
    border-radius: 4px;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

.message-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.02);
  }
}

.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .preview-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;

    .preview-image {
      max-width: 100%;
      max-height: 90vh;
      object-fit: contain;
    }
  }
}

.start-chat-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgb(0 0 0 / 75%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .modal-content {
    width: 400px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
  }

  .modal-header {
    padding: 16px;
    border-bottom: 1px solid #eee;

    .search-box {
      .input-wrapper {
        position: relative;
        width: 94%;

        input {
          width: 94%;
          padding: 8px 12px;
          padding-right: 30px; // 为清空按钮留出空间
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
          outline: none;

          &:focus {
            border-color: #ff6427;
          }

          &::placeholder {
            color: #999;
          }
        }

        .clear-btn {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 16px;
          height: 16px;
          line-height: 14px;
          text-align: center;
          background-color: #ccc;
          color: white;
          border-radius: 50%;
          cursor: pointer;
          font-size: 14px;
          font-weight: bold;

          &:hover {
            background-color: #999;
          }
        }
      }
    }
  }

  .contacts-container {
    height: 400px;
    overflow-y: auto;
    padding: 0 16px;

    .contact-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      cursor: pointer;
      border-bottom: 1px solid #f5f5f5;

      &:hover {
        background-color: #f5f5f5;
      }

      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 12px;
      }

      .name {
        font-size: 14px;
        color: #333;
      }

      .radio-box {
        width: 16px;
        height: 16px;
        border: 1px solid #ddd;
        border-radius: 50%;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;

        .radio-inner {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: transparent;
          transition: background-color 0.2s;

          &.checked {
            background-color: #ff6427;
          }
        }
      }

      &.selected {
        background-color: #fff7f5;

        .radio-box {
          border-color: #ff6427;
        }
      }
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #ddd;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }

  .modal-footer {
    padding: 16px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    button {
      padding: 8px 24px;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;

      &.cancel-btn {
        color: #666;
        background-color: #f5f5f5;
        border: none;

        &:hover {
          background-color: #eee;
        }
      }

      &.confirm-btn {
        color: white;
        background-color: #ff6427;
        border: none;

        &:disabled {
          background-color: #ffd0c1;
          cursor: not-allowed;
        }

        &:not(:disabled):hover {
          background-color: darken(#ff6427, 5%);
        }
      }
    }
  }
}

.message-status {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  text-align: right;
}
</style>